package com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger.impl;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncSlowSpeedEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.AutoSyncStockTriggerMessage;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncTriggerCommand;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO;
import org.junit.Ignore;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 多仓自动库存同步触发器测试
 *
 * <AUTHOR>
 * @date 2025/8/11 16:30
 */
@Ignore
public class MultiWarehouseAutoStockSyncTriggerTest {

    /**
     * 测试多仓库存同步触发器基本功能
     */
    @Test
    public void testMultiWarehouseTriggerSync() {
        // 创建测试数据
        MultiWarehouseAutoStockSyncTrigger trigger = new MultiWarehouseAutoStockSyncTrigger();
        
        // 构建测试请求
        AutoSyncStockTriggerMessage request = createTestTriggerMessage();
        
        // 执行触发（注意：这里只是测试方法调用，实际数据库操作需要在集成测试中验证）
        trigger.triggerSync(request);
        
        // 验证：由于涉及数据库操作，这里主要验证方法能正常执行不抛异常
        System.out.println("多仓库存同步触发器测试完成");
    }

    /**
     * 创建测试触发消息
     */
    private AutoSyncStockTriggerMessage createTestTriggerMessage() {
        AutoSyncStockTriggerMessage message = new AutoSyncStockTriggerMessage();
        message.setMemberName("test_member");
        
        List<StockSyncTriggerCommand> commands = new ArrayList<>();
        
        // 创建测试命令
        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>();
        plats.add(PolyPlatEnum.BUSINESS_Taobao);
        plats.add(PolyPlatEnum.BUSINESS_JD);
        
        command.setPlats(plats);
        command.setTriggerCount(100);
        command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        
        commands.add(command);
        message.setCommands(commands);
        
        return message;
    }

    /**
     * 创建测试平台触发数据
     */
    private List<ApiSysMatchPlatDO> createTestPlatTriggerData() {
        List<ApiSysMatchPlatDO> platData = new ArrayList<>();
        
        ApiSysMatchPlatDO platDO = new ApiSysMatchPlatDO();
        platDO.setId(1L);
        platDO.setApiSysMatchId(1001);
        platDO.setPlat(1); // 淘宝
        platDO.setShopId(1);
        platDO.setPlatWarehouseCode("WH001"); // 多仓库存的关键：平台仓库代码不为空
        platDO.setStatus(1); // 待处理状态
        
        platData.add(platDO);
        
        return platData;
    }

    /**
     * 创建测试商品匹配数据
     */
    private List<ApiSysMatchDO> createTestGoodsMatches() {
        List<ApiSysMatchDO> goodsMatches = new ArrayList<>();
        
        ApiSysMatchDO matchDO = new ApiSysMatchDO();
        matchDO.setId(1001);
        matchDO.setShopId(1);
        matchDO.setbTBGoods(1); // 淘宝平台
        matchDO.settBOuterID("TEST_OUTER_ID");
        matchDO.setSkuOuterID("TEST_SKU_OUTER_ID");
        matchDO.setNumiid("12345");
        matchDO.setSkuID("67890");
        
        goodsMatches.add(matchDO);
        
        return goodsMatches;
    }
}
