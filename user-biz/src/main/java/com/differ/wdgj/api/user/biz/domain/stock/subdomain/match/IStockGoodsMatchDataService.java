package com.differ.wdgj.api.user.biz.domain.stock.subdomain.match;

import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchExtResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchResult;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiPlatSysHisDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;

import java.util.List;
import java.util.Set;

/**
 * 商品匹配库存同步相关数据操作接口
 *
 * <AUTHOR>
 * @date 2025/7/16 11:28
 */
public interface IStockGoodsMatchDataService {
    /**
     * 查询匹配
     *
     * @param vipUser        会员名
     * @param apiSysMatchIds 匹配表主键
     * @return 结果
     */
    List<ApiSysMatchDO> queryMatch(String vipUser, List<Integer> apiSysMatchIds);

    /**
     * 批量插入库存同步日志
     *
     * @param vipUser       会员名
     * @param stockSyncLogs 库存同步日志列表
     */
    void batchInsertStockSyncLog(String vipUser, List<ApiPlatSysHisDO> stockSyncLogs);

    /**
     * 批量保存库存同步匹配结果
     *
     * @param vipUser            会员名
     * @param apiSysMatchResults 库存同步结果匹配列表
     */
    void batchSaveSyncStockResult(String vipUser, Set<StockSyncApiSysMatchResult> apiSysMatchResults);

    /**
     * 批量保存库存同步匹配扩展结果
     *
     * @param vipUser            会员名
     * @param apiSysMatchResults 库存同步结果匹配扩展列表
     */
    void batchSaveSyncStockExtResult(String vipUser, Set<StockSyncApiSysMatchExtResult> apiSysMatchResults);
}
