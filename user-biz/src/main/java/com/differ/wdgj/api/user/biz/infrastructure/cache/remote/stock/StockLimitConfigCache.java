package com.differ.wdgj.api.user.biz.infrastructure.cache.remote.stock;

import com.alibaba.fastjson.TypeReference;
import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.AbstractStringCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.core.DataCacheKeyEnum;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.DevStockLimitConfigDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.SwitchDbContext;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.center.DevStockLimitConfigMapper;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * 库存限制配置Redis缓存
 *
 * <AUTHOR>
 * @date 2025/8/4 14:03
 */
public class StockLimitConfigCache extends AbstractStringCache {
    //region 常量
    /**
     * 缓存键
     */
    private final String cacheKey;
    //endregion

    //region 构造
    /**
     * 私有构造函数
     */
    private StockLimitConfigCache() {
        cacheKey = DataCacheKeyEnum.DT_DEV_STOCK_LIMIT_CONFIG.getCode();
        // 设置缓存过期时间：24-48小时
        this.minCacheKeyTimeOut = 24 * 60 * 60;
        this.maxCacheKeyTimeOut = 48 * 60 * 60;
    }
    //endregion

    //region 单例
    /**
     * 枚举单例
     *
     * @return 库存限制配置Redis缓存单例
     */
    public static StockLimitConfigCache singleton() {
        return StockLimitConfigCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final StockLimitConfigCache instance;

        private SingletonEnum() {
            instance = new StockLimitConfigCache();
        }
    }
    //endregion

    //region 扩展方法
    /**
     * 获取所有配置
     *
     * @return 配置项列表
     */
    public List<DevStockLimitConfigDO> getAll() {
        String value = getAndSyncIfAbsent(cacheKey);
        return JsonUtils.deJson(value, new TypeReference<List<DevStockLimitConfigDO>>(){});
    }

    /**
     * 清除缓存
     */
    public void clearCache(){
        this.cacher.delete(cacheKey);
    }
    //endregion

    //region 实现基类方法
    /**
     * 获取数据源
     *
     * @param cacheKey cacheKey
     * @return 值
     */
    @Override
    protected String getSourceData(String cacheKey) {
        DevStockLimitConfigMapper mapper = BeanContextUtil.getBean(DevStockLimitConfigMapper.class);
        List<DevStockLimitConfigDO> configs =
                DBSwitchUtil.doDBWithContext(SwitchDbContext.buildEsApi(), mapper::selectAllOrderBySortNo);
        if (CollectionUtils.isEmpty(configs)){
            return null;
        }

        return JsonUtils.toJson(configs);
    }
    //endregion
}