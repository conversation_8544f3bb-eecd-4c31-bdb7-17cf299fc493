# 多仓库存同步触发器实现说明

## 概述

参考NormalAutoStockSyncTrigger实现了多仓库存同步触发器，主要用于处理需要传递仓库/门店信息的库存同步需求。

## 主要区别点

### 1. 数据源区别
- **普通库存同步**：查询`plat_warehouse_code`为空或空字符串的记录
- **多仓库存同步**：查询`plat_warehouse_code`不为空的记录

### 2. GoodsMatchEnhance构建区别
- **普通库存同步**：`multiSign`参数为空字符串
- **多仓库存同步**：需要注入平台仓库代码作为`multiSign`

## 实现的文件

### 1. 核心触发器类
**文件路径**: `user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/stock/trigger/auto/trigger/impl/MultiWarehouseAutoStockSyncTrigger.java`

**主要功能**:
- 查询多仓平台触发数据（`plat_warehouse_code`不为空）
- 构建带有平台仓库代码的GoodsMatchEnhance对象
- 处理多仓库存同步的完整流程

**关键方法**:
- `queryMultiWarehousePlatTriggerDataByCommands()`: 查询多仓触发数据
- `invokeSyncStockProcess()`: 调用库存同步处理，注入平台仓库代码

### 2. 命令解析器
**文件路径**: `user-biz/src/main/java/com/differ/wdgj/api/user/biz/domain/stock/trigger/auto/command/plugins/MultiWarehouseAutoStockSyncCommandResolver.java`

**主要功能**:
- 配置类型为`MULTI_WARE`
- 不需要特殊的命令过滤逻辑

### 3. 定时任务
**文件路径**: `user-biz/src/main/java/com/differ/wdgj/api/user/biz/tasks/job/distribute/simple/plugins/stock/auto/plugins/MultiAutoWareStockSyncTriggerJob.java`

**主要功能**:
- 定时执行多仓库存同步任务
- 执行频率：每5秒一次
- 使用`AUTO_MULTI_WAREHOUSE_STOCK_SYNC`会员解析器

### 4. 数据库查询方法
**文件路径**: `user-biz/src/main/java/com/differ/wdgj/api/user/biz/infrastructure/repository/mapper/user/ApiSysMatchPlatMapper.java`

**新增方法**: `selectMultiWarehouseByPlatformsAndStatus()`
- 查询条件：`plat_warehouse_code is not null and plat_warehouse_code != ''`

**对应XML**: `user-biz/src/main/resources/mybatis/mapper/ApiSysMatchPlatMapper.xml`

### 5. 线程池配置
**文件路径**: `user-biz/src/main/java/com/differ/wdgj/api/user/biz/infrastructure/thread/pool/TaskEnum.java`

**新增枚举**: `API_MULTI_WAREHOUSE_STOCK_SYNC`
- 线程池配置：核心线程数2倍CPU，最大线程数5倍CPU
- 队列容量：2000，报警阈值：500

### 6. 配置项
**文件路径**: `user-biz/src/main/java/com/differ/wdgj/api/user/biz/infrastructure/data/enums/ConfigKeyEnum.java`

**新增配置**: `STOCK_NOTICE_MULTI_WAREHOUSE_TRIGGER_FREQUENCY`
- 描述：多仓自动库存同步二级触发频率（单位：秒）
- 默认值：30秒

## 核心业务逻辑

### 1. 数据查询流程
```java
// 查询多仓触发数据
List<ApiSysMatchPlatDO> platTriggerData = apiSysMatchPlatMapper.selectMultiWarehouseByPlatformsAndStatus(
    platforms,
    PlatStockNoticeStatusEnum.WAIT_SYNC.getValue(),
    triggerCount
);
```

### 2. GoodsMatchEnhance构建
```java
// 构建平台仓库代码映射
Map<Integer, String> platWarehouseCodeMap = platTriggerData.stream()
    .filter(x -> x.getApiSysMatchId() != null && StringUtils.isNotBlank(x.getPlatWarehouseCode()))
    .collect(Collectors.toMap(
        ApiSysMatchPlatDO::getApiSysMatchId,
        ApiSysMatchPlatDO::getPlatWarehouseCode,
        (existing, replacement) -> existing
    ));

// 转换为GoodsMatchEnhance列表，注入平台仓库代码
List<GoodsMatchEnhance> goodsMatchEnhances = goodsMatches.stream()
    .map(x -> {
        String platWarehouseCode = platWarehouseCodeMap.getOrDefault(x.getId(), StringUtils.EMPTY);
        return GoodsMatchEnhance.create(x, platWarehouseCode);
    })
    .collect(Collectors.toList());
```

## 配置说明

### 1. 任务执行频率
- 定时任务执行频率：每5秒执行一次
- 二级触发频率：30秒（可通过配置调整）

### 2. 会员开启控制
通过`AutoMultiWarehouseStockSyncMemberResolver`控制哪些会员开启多仓库存同步功能。

### 3. 线程池配置
使用独立的线程池`API_MULTI_WAREHOUSE_STOCK_SYNC`，避免与普通库存同步任务相互影响。

## 测试

### 测试文件
**文件路径**: `user-biz/src/test/java/com/differ/wdgj/api/user/biz/domain/stock/trigger/auto/trigger/impl/MultiWarehouseAutoStockSyncTriggerTest.java`

**测试内容**:
- 基本功能测试
- 测试数据构建示例

## 使用说明

1. **数据准备**: 确保`g_api_sysmatch_plat`表中有`plat_warehouse_code`不为空的记录
2. **会员配置**: 通过`AutoMultiWarehouseStockSyncMemberResolver`配置需要开启多仓库存同步的会员
3. **监控**: 通过日志和线程池监控观察任务执行情况

## 注意事项

1. 多仓库存同步与普通库存同步使用不同的数据源和处理逻辑
2. 平台仓库代码(`plat_warehouse_code`)是区分多仓和普通库存同步的关键字段
3. GoodsMatchEnhance的`multiSign`字段承载平台仓库信息，这是多仓库存同步的核心
4. 建议根据实际业务需求调整执行频率和线程池配置
