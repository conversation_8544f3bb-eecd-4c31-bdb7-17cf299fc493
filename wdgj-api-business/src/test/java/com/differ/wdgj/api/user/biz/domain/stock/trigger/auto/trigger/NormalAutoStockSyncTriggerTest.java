package com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.PlatStockNoticeStatusEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncSlowSpeedEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.AutoSyncStockTriggerMessage;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncTriggerCommand;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger.impl.NormalAutoStockSyncTrigger;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchPlatMapper;
import org.apache.commons.collections.CollectionUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * NormalAutoStockSyncTrigger集成测试
 * 直接操作数据库，验证业务逻辑的数据库操作结果
 *
 * <AUTHOR> Generated
 * @date 2025-01-27
 */
//@Ignore
public class NormalAutoStockSyncTriggerTest extends AbstractSpringTest {
    //region 变量
    private NormalAutoStockSyncTrigger normalAutoStockSyncTrigger;

    @Autowired
    private ApiSysMatchPlatMapper apiSysMatchPlatMapper;

    private static final String TEST_MEMBER_NAME = "api2017";
    private List<Long> testDataIds = new ArrayList<>();
    //endregion

    //region 前置执行
    @Before
    public void setUp() {
        // 清理可能存在的测试数据
        cleanupTestData();

        normalAutoStockSyncTrigger = new NormalAutoStockSyncTrigger();
        apiSysMatchPlatMapper = BeanContextUtil.getBean(ApiSysMatchPlatMapper.class);
    }
    //endregion

    //region 后置执行
    @After
    public void tearDown() {
        // 清理测试数据
        cleanupTestData();
    }
    //endregion

    /**
     * 测试正常流程
     * 验证触发同步后数据库状态的变化
     */
    @Test
    public void triggerSyncNormalFlowTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 执行测试
        normalAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
        Assert.assertTrue("应该有数据被更新", updatedData.size() > 0);

        // 验证状态已更新（具体状态值需要根据实际业务逻辑确定）
        for (ApiSysMatchPlatDO platDO : updatedData) {
            Assert.assertNotEquals("状态应该已被更新", Integer.valueOf(1), platDO.getStatus());
        }
    }

    /**
     * 测试空请求处理
     */
    @Test
    public void triggerSyncWithNullRequestTest() {
        try {
            normalAutoStockSyncTrigger.triggerSync(null);
            // 如果没有抛出异常，验证数据库没有被意外修改
            List<ApiSysMatchPlatDO> data = queryTestPlatData();
            Assert.assertTrue("空请求不应该影响数据库", data.isEmpty());
        } catch (Exception e) {
            // 预期可能抛出异常，这是正常的
            Assert.assertNotNull("异常信息不应为空", e.getMessage());
        }
    }

    /**
     * 测试空命令列表处理
     */
    @Test
    public void triggerSyncWithEmptyCommandsTest() {
        // 构造空命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(new ArrayList<>());

        // 执行测试
        normalAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态
        List<ApiSysMatchPlatDO> data = queryTestPlatData();
        Assert.assertTrue("空命令不应该产生数据", data.isEmpty());
    }

    /**
     * 测试空会员名处理
     */
    @Test
    public void triggerSyncWithBlankMemberNameTest() {
        // 构造空会员名请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName("");
        request.setCommands(createTestCommands());

        try {
            normalAutoStockSyncTrigger.triggerSync(request);
            // 验证数据库状态
            List<ApiSysMatchPlatDO> data = queryTestPlatData();
            Assert.assertTrue("空会员名不应该产生数据", data.isEmpty());
        } catch (Exception e) {
            // 预期可能抛出异常
            Assert.assertNotNull("异常信息不应为空", e.getMessage());
        }
    }

    /**
     * 测试buildSlowPlatMap方法
     */
    @Test
    public void buildSlowPlatMapTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        List<StockSyncTriggerCommand> testCommands = createTestCommands();
        testCommands.forEach(x -> x.setSlowSpeed(StockSyncSlowSpeedEnum.SLOW_SPEED_1));
        request.setCommands(createTestCommands());

        // 执行测试（间接测试buildSlowPlatMap）
        normalAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库操作结果
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("数据不应为空", updatedData);
    }

    /**
     * 测试大量命令处理
     */
    @Test
    public void triggerSyncWithLargeCommandsTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造大量命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createLargeTestCommands());

        // 执行测试
        normalAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
    }

    /**
     * 测试单个命令处理
     */
    @Test
    public void triggerSyncWithSingleCommandTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造单个命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createSingleTestCommand());

        // 执行测试
        normalAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
    }

    /**
     * 测试慢速命令处理
     */
    @Test
    public void triggerSyncWithSlowSpeedCommandTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造慢速命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createSlowSpeedTestCommands());

        // 执行测试
        normalAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
    }

    /**
     * 测试多平台混合命令处理
     */
    @Test
    public void triggerSyncWithMixedPlatformCommandsTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造多平台混合命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createMixedPlatformTestCommands());

        // 执行测试
        normalAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
    }

    /**
     * 测试异常会员名处理
     */
    @Test
    public void triggerSyncWithInvalidMemberNameTest() {
        // 构造异常会员名请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName("invalid_member_name_12345");
        request.setCommands(createTestCommands());

        try {
            normalAutoStockSyncTrigger.triggerSync(request);
            // 验证数据库状态
            List<ApiSysMatchPlatDO> data = queryTestPlatData();
            Assert.assertTrue("异常会员名不应该产生数据", data.isEmpty());
        } catch (Exception e) {
            // 预期可能抛出异常
            Assert.assertNotNull("异常信息不应为空", e.getMessage());
        }
    }

    /**
     * 测试零触发数量命令处理
     */
    @Test
    public void triggerSyncWithZeroTriggerCountTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造零触发数量命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createZeroTriggerCountCommands());

        // 执行测试
        normalAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态
        List<ApiSysMatchPlatDO> data = queryTestPlatData().stream().filter(x -> PlatStockNoticeStatusEnum.SYNC_SUCCESS.getValue().equals(x.getStatus())).collect(Collectors.toList());
        // 零触发数量可能不会产生数据变化，这取决于业务逻辑
        Assert.assertTrue("查询结果应为空", CollectionUtils.isEmpty(data));
    }

    /**
     * 测试空平台集合命令处理
     */
    @Test
    public void triggerSyncWithEmptyPlatformCommandTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造空平台集合命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createEmptyPlatformCommands());

        try {
            // 执行测试
            normalAutoStockSyncTrigger.triggerSync(request);

            // 验证数据库状态
            List<ApiSysMatchPlatDO> data = queryTestPlatData().stream().filter(x -> PlatStockNoticeStatusEnum.SYNC_SUCCESS.getValue().equals(x.getStatus())).collect(Collectors.toList());
            Assert.assertTrue("查询结果应为空", CollectionUtils.isEmpty(data));
        } catch (Exception e) {
            // 空平台集合可能会抛出异常，这是正常的
            Assert.assertNotNull("异常信息不应为空", e.getMessage());
        }
    }

    /**
     * 测试null触发数量命令处理
     */
    @Test
    public void triggerSyncWithNullTriggerCountTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造null触发数量命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createNullTriggerCountCommands());

        try {
            // 执行测试
            normalAutoStockSyncTrigger.triggerSync(request);

            // 验证数据库状态
            List<ApiSysMatchPlatDO> data = queryTestPlatData().stream().filter(x -> PlatStockNoticeStatusEnum.SYNC_SUCCESS.getValue().equals(x.getStatus())).collect(Collectors.toList());
            Assert.assertTrue("查询结果应为空", CollectionUtils.isNotEmpty(data));
        } catch (Exception e) {
            // null触发数量可能会抛出异常，这是正常的
            Assert.assertNotNull("异常信息不应为空", e.getMessage());
        }
    }

    /**
     * 测试负数触发数量命令处理
     */
    @Test
    public void triggerSyncWithNegativeTriggerCountTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造负数触发数量命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createNegativeTriggerCountCommands());

        try {
            // 执行测试
            normalAutoStockSyncTrigger.triggerSync(request);

            // 验证数据库状态
            List<ApiSysMatchPlatDO> data = queryTestPlatData().stream().filter(x -> PlatStockNoticeStatusEnum.SYNC_SUCCESS.getValue().equals(x.getStatus())).collect(Collectors.toList());
            Assert.assertTrue("查询结果应为空", CollectionUtils.isEmpty(data));
        } catch (Exception e) {
            // 负数触发数量可能会抛出异常，这是正常的
            Assert.assertNotNull("异常信息不应为空", e.getMessage());
        }
    }

    /**
     * 测试极大触发数量命令处理
     */
    @Test
    public void triggerSyncWithLargeTriggerCountTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造极大触发数量命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createLargeTriggerCountCommands());

        // 执行测试
        normalAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
    }

    /**
     * 测试并发场景模拟
     */
    @Test
    public void triggerSyncConcurrentTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 模拟并发执行多次触发
        for (int i = 0; i < 3; i++) {
            AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
            request.setMemberName(TEST_MEMBER_NAME);
            request.setCommands(createTestCommands());

            try {
                normalAutoStockSyncTrigger.triggerSync(request);
            } catch (Exception e) {
                // 并发可能会产生异常，记录但不中断测试
                System.err.println("并发测试异常: " + e.getMessage());
            }
        }

        // 验证最终数据库状态
        List<ApiSysMatchPlatDO> finalData = queryTestPlatData();
        Assert.assertNotNull("最终数据不应为空", finalData);
    }

    /**
     * 测试性能 - 大数据量处理
     */
    @Test
    public void triggerSyncPerformanceTest() {
        // 准备大量测试数据
        List<ApiSysMatchPlatDO> testData = createLargeTestPlatData();
        insertTestPlatData(testData);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createLargeTestCommands());

        // 执行测试
        normalAutoStockSyncTrigger.triggerSync(request);

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;

        // 验证执行时间（假设合理的执行时间应该在10秒内）
        Assert.assertTrue("执行时间应该在合理范围内: " + executionTime + "ms", executionTime < 10000);

        // 验证数据库状态
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
    }

    /**
     * 测试数据一致性验证
     */
    @Test
    public void triggerSyncDataConsistencyTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 记录初始数据状态
        List<ApiSysMatchPlatDO> initialData = queryTestPlatData();
        int initialCount = initialData.size();

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 执行测试
        normalAutoStockSyncTrigger.triggerSync(request);

        // 验证数据一致性
        List<ApiSysMatchPlatDO> finalData = queryTestPlatData();

        // 验证数据数量没有异常变化（根据业务逻辑调整）
        Assert.assertTrue("数据数量应该保持一致或有合理变化", finalData.size() == initialCount);

        // 验证数据完整性
        for (ApiSysMatchPlatDO data : finalData) {
            Assert.assertNotNull("数据ID不应为空", data.getId());
            Assert.assertNotNull("ApiSysMatchId不应为空", data.getApiSysMatchId());
            Assert.assertNotNull("平台不应为空", data.getPlat());
        }
    }

    /**
     * 测试重复执行幂等性
     */
    @Test
    public void triggerSyncIdempotencyTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 第一次执行
        normalAutoStockSyncTrigger.triggerSync(request);
        List<ApiSysMatchPlatDO> firstResult = queryTestPlatData();

        // 第二次执行相同请求
        normalAutoStockSyncTrigger.triggerSync(request);
        List<ApiSysMatchPlatDO> secondResult = queryTestPlatData();

        // 验证幂等性（根据业务逻辑，可能需要调整验证条件）
        Assert.assertEquals("重复执行应该产生相同结果", firstResult.size(), secondResult.size());
    }

    /**
     * 测试异常恢复能力
     */
    @Test
    public void triggerSyncExceptionRecoveryTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestPlatData();
        insertTestPlatData(testData);

        // 构造可能导致异常的请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createExceptionProneCommands());

        try {
            // 执行可能异常的测试
            normalAutoStockSyncTrigger.triggerSync(request);
        } catch (Exception e) {
            // 记录异常但继续测试
            System.err.println("预期异常: " + e.getMessage());
        }

        // 验证系统能够正常处理后续请求
        AutoSyncStockTriggerMessage normalRequest = new AutoSyncStockTriggerMessage();
        normalRequest.setMemberName(TEST_MEMBER_NAME);
        normalRequest.setCommands(createTestCommands());

        // 这个调用应该能正常执行
        normalAutoStockSyncTrigger.triggerSync(normalRequest);

        // 验证数据库状态
        List<ApiSysMatchPlatDO> finalData = queryTestPlatData();
        Assert.assertNotNull("最终数据不应为空", finalData);
    }

    //region 辅助方法
    /**
     * 创建测试命令列表
     */
    private List<StockSyncTriggerCommand> createTestCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        // 创建第一个命令 - 淘宝平台
        StockSyncTriggerCommand command1 = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats1 = new HashSet<>();
        plats1.add(PolyPlatEnum.BUSINESS_Taobao);
        command1.setPlats(plats1);
        command1.setTriggerCount(100);
        command1.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command1);

        // 创建第二个命令 - 京东平台
        StockSyncTriggerCommand command2 = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats2 = new HashSet<>();
        plats2.add(PolyPlatEnum.BUSINESS_JD);
        command2.setPlats(plats2);
        command2.setTriggerCount(50);
        command2.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command2);

        return commands;
    }

    /**
     * 创建大量测试命令列表
     */
    private List<StockSyncTriggerCommand> createLargeTestCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        // 创建多个不同平台的命令
        PolyPlatEnum[] platforms = {
            PolyPlatEnum.BUSINESS_Taobao, PolyPlatEnum.BUSINESS_JD,
            PolyPlatEnum.BUSINESS_Suning, PolyPlatEnum.BUSINESS_Vipshop,
            PolyPlatEnum.BUSINESS_Mogujie
        };

        for (int i = 0; i < platforms.length; i++) {
            StockSyncTriggerCommand command = new StockSyncTriggerCommand();
            Set<PolyPlatEnum> plats = new HashSet<>();
            plats.add(platforms[i]);
            command.setPlats(plats);
            command.setTriggerCount(50 + i * 10);
            command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
            commands.add(command);
        }

        return commands;
    }

    /**
     * 创建单个测试命令
     */
    private List<StockSyncTriggerCommand> createSingleTestCommand() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>();
        plats.add(PolyPlatEnum.BUSINESS_Taobao);
        command.setPlats(plats);
        command.setTriggerCount(100);
        command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command);

        return commands;
    }

    /**
     * 创建慢速测试命令列表
     */
    private List<StockSyncTriggerCommand> createSlowSpeedTestCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        // 创建慢速命令
        StockSyncTriggerCommand command1 = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats1 = new HashSet<>();
        plats1.add(PolyPlatEnum.BUSINESS_Taobao);
        command1.setPlats(plats1);
        command1.setTriggerCount(30);
        command1.setSlowSpeed(StockSyncSlowSpeedEnum.SLOW_SPEED_1);
        commands.add(command1);

        // 创建另一个慢速命令
        StockSyncTriggerCommand command2 = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats2 = new HashSet<>();
        plats2.add(PolyPlatEnum.BUSINESS_JD);
        command2.setPlats(plats2);
        command2.setTriggerCount(20);
        command2.setSlowSpeed(StockSyncSlowSpeedEnum.SLOW_SPEED_1);
        commands.add(command2);

        return commands;
    }

    /**
     * 创建多平台混合测试命令列表
     */
    private List<StockSyncTriggerCommand> createMixedPlatformTestCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        // 创建包含多个平台的命令
        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>();
        plats.add(PolyPlatEnum.BUSINESS_Taobao);
        plats.add(PolyPlatEnum.BUSINESS_JD);
        plats.add(PolyPlatEnum.BUSINESS_Suning);
        command.setPlats(plats);
        command.setTriggerCount(150);
        command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command);

        return commands;
    }

    /**
     * 创建零触发数量测试命令列表
     */
    private List<StockSyncTriggerCommand> createZeroTriggerCountCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>();
        plats.add(PolyPlatEnum.BUSINESS_Taobao);
        command.setPlats(plats);
        command.setTriggerCount(0); // 零触发数量
        command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command);

        return commands;
    }

    /**
     * 创建空平台集合测试命令列表
     */
    private List<StockSyncTriggerCommand> createEmptyPlatformCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>(); // 空平台集合
        command.setPlats(plats);
        command.setTriggerCount(100);
        command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command);

        return commands;
    }

    /**
     * 创建null触发数量测试命令列表
     */
    private List<StockSyncTriggerCommand> createNullTriggerCountCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>();
        plats.add(PolyPlatEnum.BUSINESS_Taobao);
        command.setPlats(plats);
        command.setTriggerCount(null); // null触发数量
        command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command);

        return commands;
    }

    /**
     * 创建负数触发数量测试命令列表
     */
    private List<StockSyncTriggerCommand> createNegativeTriggerCountCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>();
        plats.add(PolyPlatEnum.BUSINESS_Taobao);
        command.setPlats(plats);
        command.setTriggerCount(-10); // 负数触发数量
        command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command);

        return commands;
    }

    /**
     * 创建极大触发数量测试命令列表
     */
    private List<StockSyncTriggerCommand> createLargeTriggerCountCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>();
        plats.add(PolyPlatEnum.BUSINESS_Taobao);
        command.setPlats(plats);
        command.setTriggerCount(Integer.MAX_VALUE); // 极大触发数量
        command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command);

        return commands;
    }

    /**
     * 创建异常倾向的测试命令列表
     */
    private List<StockSyncTriggerCommand> createExceptionProneCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        // 创建可能导致异常的命令组合
        StockSyncTriggerCommand command1 = new StockSyncTriggerCommand();
        command1.setPlats(null); // null平台集合
        command1.setTriggerCount(100);
        command1.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command1);

        StockSyncTriggerCommand command2 = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats2 = new HashSet<>();
        plats2.add(PolyPlatEnum.BUSINESS_Taobao);
        command2.setPlats(plats2);
        command2.setTriggerCount(null); // null触发数量
        command2.setSlowSpeed(null); // null慢速标识
        commands.add(command2);

        return commands;
    }

    /**
     * 创建大量测试平台数据
     */
    private List<ApiSysMatchPlatDO> createLargeTestPlatData() {
        List<ApiSysMatchPlatDO> testData = new ArrayList<>();

        // 创建更多的测试数据
        for (int i = 0; i < 10; i++) {
            ApiSysMatchPlatDO platDO = new ApiSysMatchPlatDO();
            platDO.setStatus(1); // 待处理状态
            platDO.setApiSysMatchId(1001 + i);
            platDO.setPlat(1 + (i % 3)); // 轮换平台
            platDO.setShopId(1000 + i);
            platDO.setCreateTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
            platDO.setModifiedTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
            testData.add(platDO);
        }

        return testData;
    }

    /**
     * 创建测试平台数据
     */
    private List<ApiSysMatchPlatDO> createTestPlatData() {
        List<ApiSysMatchPlatDO> testData = new ArrayList<>();
        
        ApiSysMatchPlatDO platDO1 = new ApiSysMatchPlatDO();
        platDO1.setStatus(1); // 待处理状态
        platDO1.setApiSysMatchId(1001);
        platDO1.setPlat(1);
        platDO1.setShopId(1000);
        platDO1.setCreateTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        platDO1.setModifiedTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        testData.add(platDO1);

        ApiSysMatchPlatDO platDO2 = new ApiSysMatchPlatDO();
        platDO2.setStatus(1); // 待处理状态
        platDO2.setApiSysMatchId(1002);
        platDO2.setPlat(2);
        platDO2.setShopId(1001);
        platDO2.setCreateTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        platDO2.setModifiedTime(LocalDateTime.of(2025, 1, 27, 0, 0, 0));
        testData.add(platDO2);

        return testData;
    }

    /**
     * 插入测试平台数据到数据库
     */
    private void insertTestPlatData(List<ApiSysMatchPlatDO> platData) {
        DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> {
            // 使用批量插入方法
            if (!platData.isEmpty()) {
                apiSysMatchPlatMapper.insertBatch(platData);
                // 记录插入的数据ID用于清理
                for (ApiSysMatchPlatDO platDO : platData) {
                    if (platDO.getId() != null) {
                        testDataIds.add(platDO.getId());
                    }
                }
            }
            return null;
        });
    }

    /**
     * 查询测试平台数据
     */
    private List<ApiSysMatchPlatDO> queryTestPlatData() {
        return DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> {
            List<ApiSysMatchPlatDO> result = new ArrayList<>();
            // 根据测试数据的特征查询
            List<ApiSysMatchPlatDO> data1 = apiSysMatchPlatMapper.selectByApiSysMatchId(1001);
            List<ApiSysMatchPlatDO> data2 = apiSysMatchPlatMapper.selectByApiSysMatchId(1002);
            if (data1 != null) result.addAll(data1);
            if (data2 != null) result.addAll(data2);
            return result;
        });
    }

    /**
     * 清理测试数据
     */
    private void cleanupTestData() {
        DBSwitchUtil.doDBWithUser(TEST_MEMBER_NAME, () -> {
            try {
                // 清理测试数据
                if (!testDataIds.isEmpty()) {
                    // 直接删除测试数据
                    apiSysMatchPlatMapper.deleteByIds(testDataIds);
                }
                // 清理通过apiSysMatchId查询到的数据
                List<ApiSysMatchPlatDO> data1 = apiSysMatchPlatMapper.selectByApiSysMatchId(1001);
                List<ApiSysMatchPlatDO> data2 = apiSysMatchPlatMapper.selectByApiSysMatchId(1002);
                List<Long> idsToClean = new ArrayList<>();
                if (data1 != null) {
                    for (ApiSysMatchPlatDO item : data1) {
                        idsToClean.add(item.getId());
                    }
                }
                if (data2 != null) {
                    for (ApiSysMatchPlatDO item : data2) {
                        idsToClean.add(item.getId());
                    }
                }
                if (!idsToClean.isEmpty()) {
                    // 直接删除测试数据
                    apiSysMatchPlatMapper.deleteByIds(idsToClean);
                }
            } catch (Exception e) {
                // 清理失败不影响测试
                System.err.println("清理测试数据失败: " + e.getMessage());
            }
            testDataIds.clear();
            return null;
        });
    }
    //endregion
}