<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchPlatMapper">
    <!-- 基础字段映射 -->
    <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO">
        <id property="id" column="id"/>
        <result property="status" column="status"/>
        <result property="apiSysMatchId" column="api_sys_match_id"/>
        <result property="plat" column="plat"/>
        <result property="shopId" column="shop_id"/>
        <result property="platWarehouseCode" column="plat_warehouse_code"/>
        <result property="changeSource" column="change_source"/>
        <result property="lastSuccessTime" column="last_success_time"/>
        <result property="createTime" column="create_time"/>
        <result property="modifiedTime" column="modified_time"/>
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="BaseColumnList">
        id, status, api_sys_match_id, plat, shop_id, plat_warehouse_code, change_source,
        last_success_time, create_time, modified_time
    </sql>

    <!-- 查询待执行的数据是否超过限制 -->
    <select id="getLimitNormalId" parameterType="integer" resultType="long">
        select id
        from g_api_sysmatch_plat
        where status = 1 and plat_warehouse_code is null limit #{limitMax},1
    </select>

    <!-- 查询待执行的数据是否超过限制 -->
    <select id="getLimitMultiWareId" parameterType="integer" resultType="long">
        select id
        from g_api_sysmatch_plat
        where status = 1 and plat_warehouse_code is not null limit #{limitMax},1
    </select>

    <!-- 通过匹配Id和多仓标识查询平台库存触发数据 -->
    <select id="queryByGuidAndMultiSign" parameterType="string" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from g_api_sysmatch_plat
        where
        <foreach collection="matchSignList" item="matchSign" separator="or" open="(" close=") ">
            api_sys_match_id = #{matchSign.apiSysMatchId} and plat_warehouse_code = #{matchSign.platWarehouseCode}
        </foreach>
    </select>

    <!-- 根据ApiSysMatchId查询记录 -->
    <select id="selectByApiSysMatchId" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumnList"/>
        FROM g_api_sysMatch_plat
        WHERE api_sys_match_id = #{apiSysMatchId}
    </select>

    <!-- 根据多个平台类型和状态查询待同步的触发记录 -->
    <select id="selectNormalByPlatformsAndStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumnList"/>
        FROM g_api_sysmatch_plat
        WHERE status = #{status} and plat_warehouse_code = ''
        <if test="platforms != null and platforms.size() > 0">
            AND plat IN
            <foreach collection="platforms" item="platform" open="(" close=")" separator=",">
                #{platform}
            </foreach>
        </if>
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <!-- 根据多个平台类型和状态查询多仓待同步的触发记录 -->
    <select id="selectMultiWarehouseByPlatformsAndStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="BaseColumnList"/>
        FROM g_api_sysmatch_plat
        WHERE status = #{status} and plat_warehouse_code is not null and plat_warehouse_code != ''
        <if test="platforms != null and platforms.size() > 0">
            AND plat IN
            <foreach collection="platforms" item="platform" open="(" close=")" separator=",">
                #{platform}
            </foreach>
        </if>
        ORDER BY create_time ASC
        LIMIT #{limit}
    </select>

    <!-- 插入操作 -->
    <insert id="insert" parameterType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO">
        INSERT INTO g_api_sysmatch_plat (status, api_sys_match_id, plat, shop_id, plat_warehouse_code, change_source,
        last_success_time)
        VALUES
        (#{platNotice.status}, #{platNotice.apiSysMatchId}, #{platNotice.plat}, #{platNotice.shopId},
        #{platNotice.platWarehouseCode}, #{platNotice.changeSource}, #{platNotice.lastSuccessTime})
    </insert>

    <!-- 批量插入操作 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO g_api_sysmatch_plat (status, api_sys_match_id, plat, shop_id, plat_warehouse_code, change_source,
        last_success_time)
        VALUES
        <foreach collection="platNotices" item="platNotice" separator=",">
            (#{platNotice.status}, #{platNotice.apiSysMatchId}, #{platNotice.plat}, #{platNotice.shopId},
            #{platNotice.platWarehouseCode}, #{platNotice.changeSource}, #{platNotice.lastSuccessTime})
        </foreach>
    </insert>

    <!-- 根据商品匹配关系主键修改状态和变动原因 -->
    <update id="updateStatusAndChangeReasonsById">
        <foreach collection="changeReasonMap.entrySet()" index="changeReason" item="entities" open="" close=""
                 separator=";">
            update g_api_sysmatch_plat
            set status = #{status}, change_source = ( change_source | #{changeReason} ), modified_time = now()
            where id in
            <foreach collection="entities" item="entity" separator="," open="(" close=")">
                #{entity.id}
            </foreach>
        </foreach>
    </update>

    <!-- 批量更新状态 -->
    <update id="batchUpdateStatus">
        UPDATE g_api_sysmatch_plat
        SET status = #{status}
        <if test="clearChangeSource != null and clearChangeSource == true">
            , change_source = NULL
        </if>
        , modified_time = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <!-- 根据ID列表批量删除记录 -->
    <delete id="deleteByIds">
        DELETE FROM g_api_sysmatch_plat
        WHERE id IN
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
</mapper>
