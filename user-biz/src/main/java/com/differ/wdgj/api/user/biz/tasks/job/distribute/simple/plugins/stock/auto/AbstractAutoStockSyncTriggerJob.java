package com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.plugins.stock.auto;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.user.biz.domain.stock.data.auto.AutoStockSyncDynamicData;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerJobTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.AutoSyncStockTriggerMessage;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.command.IAutoStockSyncCommandResolver;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger.IAutoStockSyncTrigger;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.job.AutoStockSyncJobShardingData;
import com.differ.wdgj.api.user.biz.tasks.job.distribute.simple.user.sharding.AbstractUserShardingSimpleDistributeJob;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 自动库存同步触发定时任务 - 抽象类
 * <p>
 * 支持多维度自动库存同步任务触发：
 * 1. 普通自动库存同步：仅需要传商品信息的库存同步
 * 2. 慢平台自动库存同步：库存同步接口非常慢的平台，会影响到其他正常平台的库存同步速率
 * 3. 多仓自动库存同步：需要传仓库/门店信息的库存同步
 *
 * <AUTHOR>
 * @date 2025/8/4 09:46
 */
public abstract class AbstractAutoStockSyncTriggerJob extends AbstractUserShardingSimpleDistributeJob<AutoStockSyncJobShardingData> {
    //region 实现抽象方法

    /**
     * 构建分片任务数据
     * <p>
     * 支持会员+其他维度的扩展场景
     * 委托给业务服务层处理复杂的分片逻辑
     *
     * @param memberNames 会员名
     * @return 分片任务数据
     */
    @Override
    protected List<AutoStockSyncJobShardingData> buildShardingJobData(List<String> memberNames) {
        if (CollectionUtils.isEmpty(memberNames)) {
            return Collections.emptyList();
        }
        // 初始化指令解析器
        IAutoStockSyncCommandResolver commandResolver = getCommandResolver();
        if (commandResolver == null) {
            return Collections.emptyList();
        }
        // 构建动态数据
        AutoStockSyncDynamicData dynamicData = new AutoStockSyncDynamicData();
        dynamicData.setCurrentExecCount(this.getJobCounter().getCurrentCount());
        // 构建任务分片
        List<AutoStockSyncJobShardingData> hardingDataList = new ArrayList<>();
        for (String memberName : memberNames) {
            try {
                List<AutoStockSyncJobShardingData> jobSharding = commandResolver.buildMemberShardingData(memberName);
                if (CollectionUtils.isNotEmpty(jobSharding)) {
                    // 注入动态数据
                    jobSharding.forEach(x -> x.setDynamicData(dynamicData));
                    hardingDataList.addAll(jobSharding);
                }
            } catch (Exception e) {
                String logContent = String.format("构建会员级分片数据失败，会员名：" + memberName);
                LogFactory.error(logCaption(), logContent, e);
            }
        }

        return hardingDataList;
    }

    /**
     * 执行分片任务
     * <p>
     * 根据任务数据触发对应的库存同步
     * 委托给业务服务层处理复杂的触发逻辑
     *
     * @param jobData 任务数据
     */
    @Override
    protected void executeShardingJob(AutoStockSyncJobShardingData jobData) {
        if (jobData == null || StringUtils.isBlank(jobData.getMemberName())) {
            return;
        }

        try {
            // 初始化指令解析器
            IAutoStockSyncCommandResolver commandResolver = getCommandResolver();
            if (commandResolver == null) {
                return;
            }

            // 构建自动库存同步触发信息
            AutoSyncStockTriggerMessage triggerMessage = commandResolver.buildStockSyncTriggerMessage(jobData);
            if (triggerMessage == null) {
                return;
            }

            // 触发库存同步
            IAutoStockSyncTrigger stockSyncTrigger = this.getAutoStockSyncTrigger();
            if (stockSyncTrigger == null) {
                return;
            }
            stockSyncTrigger.triggerSync(triggerMessage);

        } catch (Exception e) {
            String logContent = String.format("触发失败，触发数据：" + JsonUtils.toJson(jobData));
            LogFactory.error(logCaption(), logContent, e);
        }
    }

    /**
     * 日志标题
     *
     * @return 日志标题
     */
    @Override
    protected String logCaption() {
        return String.format("自动库存同步触发定时任务-%s", getAutoJobType().getName());
    }

    //endregion

    //region 抽象方法 - 供子类实现

    /**
     * 获取自动任务类型
     * <p>
     * 每个具体的自动库存同步任务需要指定对应的StockSyncTriggerJobTypeEnum
     *
     * @return 自动任务类型枚举
     */
    protected abstract StockSyncTriggerJobTypeEnum getAutoJobType();

    /**
     * 获取自动库存触发命令解析器
     *
     * @return 获取自动库存触发命令解析器
     */
    protected abstract IAutoStockSyncCommandResolver getCommandResolver();

    /**
     * 获取自动库存同步触发器
     *
     * @return 自动库存同步触发器
     */
    protected abstract IAutoStockSyncTrigger getAutoStockSyncTrigger();
    //endregion
}
