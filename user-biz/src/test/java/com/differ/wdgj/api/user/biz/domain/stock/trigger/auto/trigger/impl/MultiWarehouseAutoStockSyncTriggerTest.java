package com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger.impl;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncSlowSpeedEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.AutoSyncStockTriggerMessage;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncTriggerCommand;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO;
import org.junit.Ignore;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 多仓自动库存同步触发器测试
 *
 * <AUTHOR>
 * @date 2025/8/11 16:30
 */
@Ignore
public class MultiWarehouseAutoStockSyncTriggerTest {

    /**
     * 测试多仓库存同步触发器基本功能
     */
    @Test
    public void testMultiWarehouseTriggerSync() {
        // 创建测试数据
        MultiWarehouseAutoStockSyncTrigger trigger = new MultiWarehouseAutoStockSyncTrigger();

        // 构建测试请求
        AutoSyncStockTriggerMessage request = createTestTriggerMessage();

        // 执行触发（注意：这里只是测试方法调用，实际数据库操作需要在集成测试中验证）
        trigger.triggerSync(request);

        // 验证：由于涉及数据库操作，这里主要验证方法能正常执行不抛异常
        System.out.println("多仓库存同步触发器测试完成");
    }

    /**
     * 测试一对多仓库代码映射逻辑
     * 验证一个apiSysMatchId对应多个平台仓库代码的场景
     */
    @Test
    public void testOneToManyWarehouseMapping() {
        // 模拟测试数据
        List<ApiSysMatchPlatDO> platTriggerData = createTestPlatTriggerData();
        List<ApiSysMatchDO> goodsMatches = createTestGoodsMatches();

        // 验证测试数据：一个apiSysMatchId(1001)对应三个仓库代码(WH001, WH002, WH003)
        long matchId1001Count = platTriggerData.stream()
                .filter(x -> x.getApiSysMatchId().equals(1001))
                .count();

        System.out.println("apiSysMatchId=1001 对应的仓库数量: " + matchId1001Count);

        // 验证应该有3个仓库代码
        assert matchId1001Count == 3 : "应该有3个仓库代码对应同一个apiSysMatchId";

        // 验证仓库代码不重复
        Set<String> warehouseCodes = platTriggerData.stream()
                .filter(x -> x.getApiSysMatchId().equals(1001))
                .map(ApiSysMatchPlatDO::getPlatWarehouseCode)
                .collect(Collectors.toSet());

        System.out.println("仓库代码集合: " + warehouseCodes);
        assert warehouseCodes.size() == 3 : "仓库代码应该不重复";
        assert warehouseCodes.contains("WH001") : "应该包含WH001";
        assert warehouseCodes.contains("WH002") : "应该包含WH002";
        assert warehouseCodes.contains("WH003") : "应该包含WH003";

        System.out.println("一对多仓库代码映射逻辑测试通过");
    }

    /**
     * 创建测试触发消息
     */
    private AutoSyncStockTriggerMessage createTestTriggerMessage() {
        AutoSyncStockTriggerMessage message = new AutoSyncStockTriggerMessage();
        message.setMemberName("test_member");
        
        List<StockSyncTriggerCommand> commands = new ArrayList<>();
        
        // 创建测试命令
        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats = new HashSet<>();
        plats.add(PolyPlatEnum.BUSINESS_Taobao);
        plats.add(PolyPlatEnum.BUSINESS_JD);
        
        command.setPlats(plats);
        command.setTriggerCount(100);
        command.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        
        commands.add(command);
        message.setCommands(commands);
        
        return message;
    }

    /**
     * 创建测试平台触发数据
     * 测试一对多的场景：一个apiSysMatchId对应多个平台仓库代码
     */
    private List<ApiSysMatchPlatDO> createTestPlatTriggerData() {
        List<ApiSysMatchPlatDO> platData = new ArrayList<>();

        // 第一个仓库
        ApiSysMatchPlatDO platDO1 = new ApiSysMatchPlatDO();
        platDO1.setId(1L);
        platDO1.setApiSysMatchId(1001);
        platDO1.setPlat(1); // 淘宝
        platDO1.setShopId(1);
        platDO1.setPlatWarehouseCode("WH001"); // 第一个仓库代码
        platDO1.setStatus(1); // 待处理状态

        // 第二个仓库（同一个apiSysMatchId）
        ApiSysMatchPlatDO platDO2 = new ApiSysMatchPlatDO();
        platDO2.setId(2L);
        platDO2.setApiSysMatchId(1001); // 相同的apiSysMatchId
        platDO2.setPlat(1); // 淘宝
        platDO2.setShopId(1);
        platDO2.setPlatWarehouseCode("WH002"); // 第二个仓库代码
        platDO2.setStatus(1); // 待处理状态

        // 第三个仓库（同一个apiSysMatchId）
        ApiSysMatchPlatDO platDO3 = new ApiSysMatchPlatDO();
        platDO3.setId(3L);
        platDO3.setApiSysMatchId(1001); // 相同的apiSysMatchId
        platDO3.setPlat(1); // 淘宝
        platDO3.setShopId(1);
        platDO3.setPlatWarehouseCode("WH003"); // 第三个仓库代码
        platDO3.setStatus(1); // 待处理状态

        platData.add(platDO1);
        platData.add(platDO2);
        platData.add(platDO3);

        return platData;
    }

    /**
     * 创建测试商品匹配数据
     */
    private List<ApiSysMatchDO> createTestGoodsMatches() {
        List<ApiSysMatchDO> goodsMatches = new ArrayList<>();
        
        ApiSysMatchDO matchDO = new ApiSysMatchDO();
        matchDO.setId(1001);
        matchDO.setShopId(1);
        matchDO.setbTBGoods(1); // 淘宝平台
        matchDO.settBOuterID("TEST_OUTER_ID");
        matchDO.setSkuOuterID("TEST_SKU_OUTER_ID");
        matchDO.setNumiid("12345");
        matchDO.setSkuID("67890");
        
        goodsMatches.add(matchDO);
        
        return goodsMatches;
    }
}
