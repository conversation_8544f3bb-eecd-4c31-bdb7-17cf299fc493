package com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.command.plugins;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncSlowSpeedEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerConfigTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncTriggerCommand;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.command.AbstractAutoStockSyncCommandResolver;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 自动库存同步触发命令处理 - 慢平台库存同步
 *
 * <AUTHOR>
 * @date 2025/8/11 13:58
 */
public class SlowPlatAutoStockSyncCommandResolver extends AbstractAutoStockSyncCommandResolver {
    /**
     * 获取配置类型
     *
     * @return 配置类型
     */
    @Override
    protected StockSyncTriggerConfigTypeEnum getConfigType() {
        return StockSyncTriggerConfigTypeEnum.NORMAL;
    }


    /**
     * 过滤命令
     *
     * @param commands 命令列表
     */
    @Override
    protected List<StockSyncTriggerCommand> filterCommand(List<StockSyncTriggerCommand> commands) {
        return commands.stream().filter(command -> command.getSlowSpeed() == StockSyncSlowSpeedEnum.SLOW_SPEED_1).collect(Collectors.toList());
    }
}
