package com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.command;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncJobShardingTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncSlowSpeedEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerConfigTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.AutoSyncStockTriggerMessage;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncTriggerCommand;
import com.differ.wdgj.api.user.biz.domain.stock.utils.SyncStockConfigUtils;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.common.SkipExecuteControlLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.shop.AllPlatLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.common.SkipExecuteControl;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.StockLimitConfigLocalDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.StockTriggerConfigLocalDto;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.job.AutoStockSyncJobShardingData;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 自动库存同步触发命令处理抽象基类
 * <p>
 * 提供通用的业务逻辑实现和模板方法，子类只需实现特定的业务逻辑
 * <a href="https://s.jkyun.biz/SvAOU6F">【管家java】开发设计 - 自动库存同步任务 - 库存同步发起数据构建</a>
 *
 * <AUTHOR>
 * @date 2025/8/4 14:03
 */
public abstract class AbstractAutoStockSyncCommandResolver implements IAutoStockSyncCommandResolver {
    //region 常量
    /**
     * 库存同步配置操作者
     */
    private final SyncStockConfigUtils syncStockTriggerConfigOperator;

    /**
     * 会员所有平台缓存
     */
    private final AllPlatLocalCache allPlatLocalCache;

    /**
     * 跳跃执行控制缓存
     */
    private final SkipExecuteControlLocalCache skipExecuteControlLocalCache;
    //endregion

    //region 构造
    public AbstractAutoStockSyncCommandResolver() {
        this.syncStockTriggerConfigOperator = SyncStockConfigUtils.singleton();
        this.allPlatLocalCache = AllPlatLocalCache.singleton();
        this.skipExecuteControlLocalCache = SkipExecuteControlLocalCache.singleton();
    }

    public AbstractAutoStockSyncCommandResolver(SyncStockConfigUtils syncStockTriggerConfigOperator, AllPlatLocalCache allPlatLocalCache, SkipExecuteControlLocalCache skipExecuteControlLocalCache) {
        this.syncStockTriggerConfigOperator = syncStockTriggerConfigOperator;
        this.allPlatLocalCache = allPlatLocalCache;
        this.skipExecuteControlLocalCache = skipExecuteControlLocalCache;
    }
    //endregion

    //region 模板方法实现

    /**
     * 构建会员级任务分片数据
     * <p>
     * 模板方法，定义了构建分片数据的标准流程：
     *
     * @param memberName 会员名
     * @return 会员的分片任务数据列表
     */
    @Override
    public final List<AutoStockSyncJobShardingData> buildMemberShardingData(String memberName) {
        if (StringUtils.isBlank(memberName)) {
            return Collections.emptyList();
        }

        // 会员级分片
        AutoStockSyncJobShardingData jobSharding = createJobSharding(StockSyncJobShardingTypeEnum.MEMBER, memberName, memberName);
        // 构建会员级控制系数
        StockTriggerConfigLocalDto stockTriggerConfig = syncStockTriggerConfigOperator.getStockTriggerConfig(getConfigType(), memberName);
        if (stockTriggerConfig != null) {
            jobSharding.setControlFactor(stockTriggerConfig.getControlFactor().toString());
        }
        return Collections.singletonList(jobSharding);
    }

    /**
     * 构建库存同步触发消息
     * <p>
     * 模板方法，定义了构建触发消息的标准流程：
     *
     * @param jobData 任务分片数据
     * @return 库存同步触发消息
     */
    @Override
    public final AutoSyncStockTriggerMessage buildStockSyncTriggerMessage(AutoStockSyncJobShardingData jobData) {
        if (jobData == null || StringUtils.isEmpty(jobData.getMemberName())) {
            return null;
        }

        // 构建会员级分片库存同步触发消息
        List<StockSyncTriggerCommand> memberCommands = buildMemberCommand(jobData);

        // 触发消息过滤
        List<StockSyncTriggerCommand> commands = filterCommand(memberCommands);
        if (CollectionUtils.isEmpty(commands)) {
            return null;
        }

        // 构建库存同步触发消息
        AutoSyncStockTriggerMessage triggerMessage = new AutoSyncStockTriggerMessage();
        triggerMessage.setMemberName(jobData.getMemberName());
        triggerMessage.setCommands(commands);
        return triggerMessage;
    }

    //endregion

    //region 会员级分片库存同步触发消息

    /**
     * 构建会员级分片库存同步触发消息
     *
     * @param jobData 任务分片数据
     * @return 库存同步触发消息
     */
    private List<StockSyncTriggerCommand> buildMemberCommand(AutoStockSyncJobShardingData jobData) {
        // 基础参数
        String memberName = jobData.getMemberName();
        Set<PolyPlatEnum> platAll = allPlatLocalCache.getAllPlats(memberName);
        List<StockSyncTriggerCommand> commands = new ArrayList<>();

        // 构建平台级配置
        Set<PolyPlatEnum> specialPlats = new HashSet<>();
        List<StockLimitConfigLocalDto> stockLimitConfigs = syncStockTriggerConfigOperator.getStockLimitConfigs(memberName);
        for (StockLimitConfigLocalDto stockLimitConfig : stockLimitConfigs) {
            // 匹配平台
            Set<PolyPlatEnum> matchedPlats = new HashSet<>();
            for (PolyPlatEnum plat : platAll) {
                // 平台级匹配限流配置
                if (stockLimitConfig.getPlat().stream().anyMatch(plat.getValue().toString()::equalsIgnoreCase) ||
                        stockLimitConfig.getPlat().stream().anyMatch(ConfigKeyUtils.ALL::equalsIgnoreCase)) {
                    // 记录特殊配置的平台
                    specialPlats.add(plat);
                    if (jobData.getDynamicData() != null) {
                        // 降频拦截
                        int currentExecCount = jobData.getDynamicData().getCurrentExecCount();
                        if (forbidByDowngradeFrequency(currentExecCount, plat, stockLimitConfig)) {
                            continue;
                        }
                    }
                    matchedPlats.add(plat);
                }
            }

            // 构建平台级触发命令
            if (CollectionUtils.isNotEmpty(matchedPlats)) {
                commands.add(createTriggerCommand(matchedPlats, stockLimitConfig.getTriggerCount(), stockLimitConfig.getSlowSpeedSign()));
            }
        }

        // 构建会员级触发命令
        StockTriggerConfigLocalDto stockTriggerConfig = syncStockTriggerConfigOperator.getStockTriggerConfig(getConfigType(), memberName);
        if (stockTriggerConfig != null) {
            // 剔除平台级触发命令相关平台
            Set<PolyPlatEnum> memberPlat = platAll.stream().filter(x -> !specialPlats.contains(x)).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(memberPlat)) {
                commands.add(createTriggerCommand(memberPlat, stockTriggerConfig.getTriggerCount(), StockSyncSlowSpeedEnum.NORMAL_SPEED_0));
            }
        }

        return commands;
    }

    /**
     * 降频拦截
     *
     * @param currentExecCount 当前执行次数
     * @param plat             平台
     * @param limitConfig      限流配置
     * @return 是否拦截
     */
    private boolean forbidByDowngradeFrequency(int currentExecCount, PolyPlatEnum plat, StockLimitConfigLocalDto limitConfig) {
        // 基础校验
        if (MapUtils.isEmpty(limitConfig.getDowngradeFactors())) {
            return false;
        }
        // 获取降频系数
        String controlFactor = limitConfig.getDowngradeFactors().getOrDefault(plat, null);
        if (StringUtils.isEmpty(controlFactor)) {
            return false;
        }
        // 获取执行控制
        SkipExecuteControl control = skipExecuteControlLocalCache.getControl(controlFactor);
        if (control == null) {
            return false;
        }
        // 降频拦截
        return control.forbid(currentExecCount);
    }
    //endregion

    //region 供子类重写方法

    /**
     * 获取配置类型
     *
     * @return 配置类型
     */
    protected abstract StockSyncTriggerConfigTypeEnum getConfigType();

    /**
     * 过滤命令
     *
     * @param commands 命令列表
     */
    protected List<StockSyncTriggerCommand> filterCommand(List<StockSyncTriggerCommand> commands) {
        return commands;
    }

    //endregion

    // region 保护方法

    /**
     * 创建任务分片
     *
     * @param shardingType 分片类型
     * @param shardingSign 分片标识
     * @param memberName   会员名
     * @return 结果
     */
    protected AutoStockSyncJobShardingData createJobSharding(StockSyncJobShardingTypeEnum shardingType, String shardingSign, String memberName) {
        AutoStockSyncJobShardingData jobSharding = new AutoStockSyncJobShardingData();
        jobSharding.setShardingType(shardingType);
        jobSharding.setMemberName(memberName);
        return jobSharding;
    }

    /**
     * 创建触发命令
     *
     * @param plats        平台
     * @param triggerCount 触发数量
     * @param slowSpeed    慢速
     * @return 结果
     */
    protected StockSyncTriggerCommand createTriggerCommand(Set<PolyPlatEnum> plats, Integer triggerCount, StockSyncSlowSpeedEnum slowSpeed) {
        StockSyncTriggerCommand command = new StockSyncTriggerCommand();
        command.setPlats(plats);
        command.setTriggerCount(triggerCount);
        command.setSlowSpeed(slowSpeed);
        return command;
    }

    // endregion
}