<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchMapper" >

    <!--匹配表数据映射-->
    <resultMap id="BaseResultMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO" >
        <id column="Id" property="id" jdbcType="INTEGER" />
        <result column="shopId" property="shopId" jdbcType="INTEGER" />
        <result column="bTBGoods" property="bTBGoods" jdbcType="INTEGER" />
        <result column="isSys" property="isSys" jdbcType="INTEGER" />
        <result column="synFlag" property="synFlag" jdbcType="INTEGER" />
        <result column="bGetStock" property="bGetStock" jdbcType="TINYINT" />
        <result column="sysCount" property="sysCount" jdbcType="INTEGER" />
        <result column="sysLog" property="sysLog" jdbcType="VARCHAR" />
        <result column="updatetime" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="lastModify" property="lastModify" jdbcType="TIMESTAMP" />
        <result column="goodsID" property="goodsID" jdbcType="INTEGER" />
        <result column="specId" property="specID" jdbcType="INTEGER" />
        <result column="goodsType" property="goodsType" jdbcType="INTEGER" />
        <result column="numiid" property="numiid" jdbcType="VARCHAR" />
        <result column="skuID" property="skuID" jdbcType="VARCHAR" />
        <result column="tBName" property="tBName" jdbcType="VARCHAR" />
        <result column="tBSku" property="tBSku" jdbcType="VARCHAR" />
        <result column="tBOuterID" property="tBOuterID" jdbcType="VARCHAR" />
        <result column="skuOuterID" property="skuOuterID" jdbcType="VARCHAR" />
        <result column="sysGoodsType" property="sysGoodsType" jdbcType="VARCHAR" />
        <result column="subShopID" property="subShopID" jdbcType="VARCHAR" />
        <result column="sendType" property="sendType" jdbcType="VARCHAR" />
        <result column="whseCode" property="whseCode" jdbcType="VARCHAR" />
        <result column="cooperationNo" property="cooperationNo" jdbcType="VARCHAR" />
        <result column="warehouseFlag" property="warehouseFlag" jdbcType="INTEGER" />
        <result column="shelfState" property="shelfState" jdbcType="INTEGER"/>
        <result column="bFixNum" property="bFixNum" jdbcType="INTEGER" />
        <result column="fixNum" property="fixNum" jdbcType="INTEGER" />
        <result column="bVirNum" property="bVirNum" jdbcType="INTEGER" />
        <result column="virNumBase" property="virNumBase" jdbcType="INTEGER" />
        <result column="virNumInc" property="virNumInc" jdbcType="INTEGER" />
        <result column="bstop" property="bstop" jdbcType="INTEGER" />
        <result column="bSingletb" property="bSingletb" jdbcType="INTEGER" />
        <result column="singleNumPer" property="singleNumPer" jdbcType="INTEGER" />
        <result column="virNumTop" property="virNumTop" jdbcType="INTEGER" />
        <result column="bruleStop" property="bruleStop" jdbcType="TINYINT" />
        <result column="ruleWarehouse" property="ruleWarehouse" jdbcType="VARCHAR" />
    </resultMap>

    <!--带匹配扩展数据的结果映射-->
    <resultMap id="BaseResultWithExtraMap" type="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO" >
        <id column="Id" property="id" jdbcType="INTEGER" />
        <result column="shopId" property="shopId" jdbcType="INTEGER" />
        <result column="bTBGoods" property="bTBGoods" jdbcType="INTEGER" />
        <result column="isSys" property="isSys" jdbcType="INTEGER" />
        <result column="synFlag" property="synFlag" jdbcType="INTEGER" />
        <result column="bGetStock" property="bGetStock" jdbcType="TINYINT" />
        <result column="sysCount" property="sysCount" jdbcType="INTEGER" />
        <result column="sysLog" property="sysLog" jdbcType="VARCHAR" />
        <result column="updatetime" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="lastModify" property="lastModify" jdbcType="TIMESTAMP" />
        <result column="goodsID" property="goodsID" jdbcType="INTEGER" />
        <result column="specId" property="specID" jdbcType="INTEGER" />
        <result column="goodsType" property="goodsType" jdbcType="INTEGER" />
        <result column="numiid" property="numiid" jdbcType="VARCHAR" />
        <result column="skuID" property="skuID" jdbcType="VARCHAR" />
        <result column="tBName" property="tBName" jdbcType="VARCHAR" />
        <result column="tBSku" property="tBSku" jdbcType="VARCHAR" />
        <result column="tBOuterID" property="tBOuterID" jdbcType="VARCHAR" />
        <result column="skuOuterID" property="skuOuterID" jdbcType="VARCHAR" />
        <result column="sysGoodsType" property="sysGoodsType" jdbcType="VARCHAR" />
        <result column="subShopID" property="subShopID" jdbcType="VARCHAR" />
        <result column="sendType" property="sendType" jdbcType="VARCHAR" />
        <result column="whseCode" property="whseCode" jdbcType="VARCHAR" />
        <result column="cooperationNo" property="cooperationNo" jdbcType="VARCHAR" />
        <result column="warehouseFlag" property="warehouseFlag" jdbcType="INTEGER" />
        <result column="shelfState" property="shelfState" jdbcType="INTEGER"/>
        <result column="bFixNum" property="bFixNum" jdbcType="INTEGER" />
        <result column="fixNum" property="fixNum" jdbcType="INTEGER" />
        <result column="bVirNum" property="bVirNum" jdbcType="INTEGER" />
        <result column="virNumBase" property="virNumBase" jdbcType="INTEGER" />
        <result column="virNumInc" property="virNumInc" jdbcType="INTEGER" />
        <result column="bstop" property="bstop" jdbcType="INTEGER" />
        <result column="bSingletb" property="bSingletb" jdbcType="INTEGER" />
        <result column="singleNumPer" property="singleNumPer" jdbcType="INTEGER" />
        <result column="virNumTop" property="virNumTop" jdbcType="INTEGER" />
        <result column="bruleStop" property="bruleStop" jdbcType="TINYINT" />
        <result column="ruleWarehouse" property="ruleWarehouse" jdbcType="VARCHAR" />
        <association property="extEntity" column="id"
                     javaType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchExtDO">
            <id column="extId" property="id" jdbcType="INTEGER" />
            <result column="rel_item_id" property="relItemId" jdbcType="INTEGER" />
            <result column="platStoreCodes" property="platStoreCodes" jdbcType="VARCHAR" />
            <result column="platStoreNames" property="platStoreNames" jdbcType="VARCHAR" />
            <result column="nextResetSyncTime" property="nextResetSyncTime" jdbcType="TIMESTAMP" />
            <result column="increFlag" property="increFlag" jdbcType="INTEGER" />
            <result column="restrictedMode" property="restrictedMode" jdbcType="INTEGER" />
            <result column="platGoodsType" property="platGoodsType" jdbcType="INTEGER" />
            <result column="jsonParams" property="jsonParams" jdbcType="VARCHAR" />
        </association>
    </resultMap>

    <!-- 批量获取商品匹配 + 商品匹配扩展 -->
    <select id="selectWithExtraByIds"
            parameterType="java.lang.Integer"
            resultMap="BaseResultWithExtraMap">
        select sysmatch.ID,Numiid,SkuID,GoodsID,SpecID,TBName,TBSku,TBOuterID,SKUOuterID,IsSys,bGetStock,SysLog,ShopID,SubShopId,bTBGoods,updatetime,
               SysCount,SysGoodsType,bFixNum,FixNum,bVirNum,VirNumBase,VirNumInc,bstop,GoodsType,bSingletb,SingleNumPer,VirNumTop,SendType,
               LastModify,WhseCode,CooperationNo,ShelfState,brulestop,ruleWarehouse,synflag,
               sysmatchext.Id as extId,rel_item_id,PlatStoreCodes,PlatStoreNames,JsonParams,nextResetSyncTime,increFlag,PlatGoodsType,restrictedMode
        from g_api_sysmatch sysmatch
        left join g_api_sysmatchext sysmatchext ON sysmatch.Id = sysmatchext.Id
        where sysmatch.Id in
        <foreach collection="ids" item="Id" open="(" close=")" separator=",">
            #{Id}
        </foreach>
    </select>

    <!-- 批量获取商品匹配 + 商品匹配扩展 -->
    <select id="selectWithExtraByErpNotices"
            parameterType="java.lang.Integer"
            resultMap="BaseResultWithExtraMap">
        select sysmatch.ID,Numiid,SkuID,sysmatch.GoodsID,sysmatch.SpecID,TBName,TBSku,TBOuterID,SKUOuterID,sysmatch.IsSys,bGetStock,SysLog,ShopID,SubShopId,bTBGoods,sysmatch.updatetime,
        SysCount,SysGoodsType,bFixNum,FixNum,bVirNum,VirNumBase,VirNumInc,bstop,sysmatch.GoodsType,bSingletb,SingleNumPer,VirNumTop,SendType,
        LastModify,WhseCode,CooperationNo,ShelfState,brulestop,ruleWarehouse,synflag,
        sysmatchext.Id as extId,rel_item_id,PlatStoreCodes,PlatStoreNames,JsonParams,nextResetSyncTime,increFlag,PlatGoodsType,restrictedMode
        from g_api_sysmatch sysmatch
        join g_api_sysmatch_temp erpNotice on sysmatch.GoodsID = erpnotice.GoodsID and sysmatch.SpecID = erpnotice.SpecID and sysmatch.GoodsType = erpnotice.GoodsType
        left join g_api_sysmatchext sysmatchext ON sysmatch.Id = sysmatchext.Id
        where sysmatch.ID > #{startId} and erpNotice.Id in
        <foreach collection="erpNoticeIds" item="erpNoticeId" open="(" close=")" separator=",">
            #{erpNoticeId}
        </foreach>
        order by sysmatch.ID
        limit #{pageSize}
    </select>

    <!-- 基于平台商品Id批量获取商品匹配 -->
    <select id="selectWithExtraByNumIIds"
            parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select sysmatch.ID,Numiid,SkuID,GoodsID,SpecID,TBName,TBSku,TBOuterID,SKUOuterID,IsSys,bGetStock,SysLog,ShopID,SubShopId,bTBGoods,updatetime,
               SysCount,SysGoodsType,bFixNum,FixNum,bVirNum,VirNumBase,VirNumInc,bstop,GoodsType,bSingletb,SingleNumPer,VirNumTop,SendType,
               LastModify,WhseCode,CooperationNo,ShelfState,brulestop,ruleWarehouse,synflag
        from g_api_sysmatch sysmatch
        where sysmatch.Numiid in
        <foreach collection="platGoodsIds" item="platGoodsId" open="(" close=")" separator=",">
            #{platGoodsId}
        </foreach>
    </select>

    <!--通过平台规格Id批量获取商品匹配-->
    <select id="selectWithExtraBySkuIds" parameterType="java.lang.String" resultType="com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO">
        select
        sysmatch.ID,Numiid,SkuID,GoodsID,SpecID,TBName,TBSku,TBOuterID,SKUOuterID,IsSys,bGetStock,SysLog,ShopID,SubShopId,bTBGoods,
        SysCount,SysGoodsType,bFixNum,FixNum,bVirNum,VirNumBase,VirNumInc,bstop,GoodsType,bSingletb,SingleNumPer,VirNumTop,SendType,
        LastModify,WhseCode,CooperationNo,ShelfState,brulestop,ruleWarehouse,synflag
        from g_api_sysmatch sysmatch
        where 1 = 1
        <if test="platSkuIds != null and platSkuIds.size() > 0">
            and sysmatch.SkuID in
            <foreach collection="platSkuIds" item="platSkuId" open="(" close=")" separator=",">
                #{platSkuId}
            </foreach>
        </if>
        <if test="platGoodsIds != null and platGoodsIds.size() > 0">
            and sysmatch.Numiid in
            <foreach collection="platGoodsIds" item="platGoodsId" open="(" close=")" separator=",">
                #{platGoodsId}
            </foreach>
        </if>
    </select>

    <!--通过平台规格Id批量获取商品匹配-->
    <select id="selectWithExtraByPlatIds"
            parameterType="java.lang.String"
            resultType="com.differ.wdgj.api.user.biz.domain.aftersale.save.data.source.AfterSaleGoodsSysMatchItem">
        select sysmatch.ID,Numiid,SkuID,GoodsID,SpecID,GoodsType,TBName,TBSku,TBOuterID,SKUOuterID
        from g_api_sysmatch sysmatch
        where 1 = 1 
        <if test="platSkuIds != null and platSkuIds.size() > 0">
            and sysmatch.SkuID in
            <foreach collection="platSkuIds" item="platSkuId" open="(" close=")" separator=",">
                #{platSkuId}
            </foreach>
        </if>
        <if test="platGoodsIds != null and platGoodsIds.size() > 0">
            and sysmatch.Numiid in
            <foreach collection="platGoodsIds" item="platGoodsId" open="(" close=")" separator=",">
                #{platGoodsId}
            </foreach>
        </if>
    </select>

    <!-- 批量更新停用状态 -->
    <update id="updateStopState">
        UPDATE g_api_sysMatch SET bStop = #{stopState} WHERE bStop != #{stopState} And id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <!-- 批量更新上下架状态 -->
    <update id="updateShelfState">
        UPDATE g_api_sysMatch SET ShelfState = #{shelfState} WHERE ShelfState != #{shelfState}  And id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <!-- 批量更新库存同步结果 -->
    <update id="updateSyncStockResult" parameterType="com.differ.wdgj.api.user.biz.domain.stock.data.result.StockSyncApiSysMatchResult">
        UPDATE g_api_sysMatch M JOIN
        (
        <foreach collection="apiSysMatchResults" item="apiSysMatchResult" separator=" UNION ">
            SELECT
            #{apiSysMatchResult.apiSysMatchId} AS Id,
            #{apiSysMatchResult.isSys} AS isSys,
            #{apiSysMatchResult.sysLog} AS sysLog,
            #{apiSysMatchResult.sysGoodsType} AS sysGoodsType,
            #{apiSysMatchResult.synFlag} AS synFlag,
            #{apiSysMatchResult.sysCount} AS sysCount
        </foreach>
        ) N USING(Id)
        SET
        M.isSys = N.isSys,
        M.sysLog = N.sysLog,
        M.sysGoodsType = N.sysGoodsType,
        M.synFlag = N.synFlag,
        M.sysCount = N.sysCount,
        M.updateTime = NOW()
        WHERE M.isSys = 99
    </update>

    <!-- 修改商品匹配合作编码 -->
    <update id="updateGoodsCooperationNo" parameterType="com.differ.wdgj.api.user.biz.domain.goods.match.data.ApiSysMatchCooperationNoEnhance">
        UPDATE g_api_sysMatch M JOIN
        (
        <foreach collection="cooperationNos" item="cooperationNoItem" separator=" UNION ">
            SELECT
            #{cooperationNoItem.id} AS id,
            #{cooperationNoItem.cooperationNo} AS cooperationNo
        </foreach>
        ) N USING(Id)
        SET
        M.cooperationNo = N.cooperationNo
        WHERE M.Id = N.Id
    </update>

    <!-- 修改商品的平台类型枚举 -->
    <update id="updateGoodsPolyType">
        UPDATE g_api_sysMatch SET bTBGoods = #{goodsPolyType} WHERE bTBGoods != #{goodsPolyType} And id in
        <foreach collection="matchIds" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <!-- 根据主键删除商品匹配 -->
    <delete id="deleteById" parameterType="java.lang.Integer">
        delete from g_api_sysmatch where id in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </delete>
</mapper>