// 简单的编译测试文件，验证修复后的类型是否正确
import java.util.*;

// 模拟相关类的简化版本
class PolyPlatEnum {
    public static final PolyPlatEnum BUSINESS_Taobao = new PolyPlatEnum();
    public static final PolyPlatEnum BUSINESS_JD = new PolyPlatEnum();
}

class StockSyncSlowSpeedEnum {
    public static final StockSyncSlowSpeedEnum NORMAL_SPEED_0 = new StockSyncSlowSpeedEnum();
}

class StockSyncTriggerCommand {
    private Set<PolyPlatEnum> plats;
    private Integer triggerCount;
    private StockSyncSlowSpeedEnum slowSpeed;
    
    public void setPlats(Set<PolyPlatEnum> plats) { this.plats = plats; }
    public void setTriggerCount(Integer triggerCount) { this.triggerCount = triggerCount; }
    public void setSlowSpeed(StockSyncSlowSpeedEnum slowSpeed) { this.slowSpeed = slowSpeed; }
}

class AutoSyncStockTriggerMessage {
    private String memberName;
    private List<StockSyncTriggerCommand> commands;
    
    public void setMemberName(String memberName) { this.memberName = memberName; }
    public void setCommands(List<StockSyncTriggerCommand> commands) { this.commands = commands; }
}

public class test_compilation {
    // 测试修复后的代码逻辑
    private List<StockSyncTriggerCommand> createTestCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();
        
        // 创建第一个命令 - 淘宝平台
        StockSyncTriggerCommand command1 = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats1 = new HashSet<>();
        plats1.add(PolyPlatEnum.BUSINESS_Taobao);
        command1.setPlats(plats1);
        command1.setTriggerCount(100);
        command1.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command1);
        
        // 创建第二个命令 - 京东平台
        StockSyncTriggerCommand command2 = new StockSyncTriggerCommand();
        Set<PolyPlatEnum> plats2 = new HashSet<>();
        plats2.add(PolyPlatEnum.BUSINESS_JD);
        command2.setPlats(plats2);
        command2.setTriggerCount(50);
        command2.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL_SPEED_0);
        commands.add(command2);
        
        return commands;
    }
    
    public void testCorrectUsage() {
        // 测试正确的用法
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName("api2017");
        request.setCommands(createTestCommands()); // 正确：List<StockSyncTriggerCommand>
        
        // 测试空列表
        request.setCommands(new ArrayList<StockSyncTriggerCommand>());
        
        System.out.println("编译测试通过！类型修复正确。");
    }
    
    public static void main(String[] args) {
        new test_compilation().testCorrectUsage();
    }
}
