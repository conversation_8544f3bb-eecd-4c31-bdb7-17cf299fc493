package com.differ.wdgj.api.user.biz.domain.stock.processor.request.mode;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.tools.StopWatchUtils;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ApiCallResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockRequestGoodInfo;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.BusinessBatchSyncStockResponseGoodSyncStockResultItem;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.PolyAPIBusinessBatchSyncStockRequest;
import com.differ.wdgj.api.user.biz.domain.apicall.data.ployapi.business.batchSyncStock.PolyAPIBusinessBatchSyncStockResponse;
import com.differ.wdgj.api.user.biz.domain.apicall.plugins.stock.BatchSyncStockApiCall;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.request.StockSyncRequestGroup;
import com.differ.wdgj.api.user.biz.domain.stock.data.result.GoodsStockCalculationResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncContext;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncMatchExecuteResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncRequestItem;
import com.differ.wdgj.api.user.biz.domain.stock.processor.BaseSyncStockProcessor;
import com.differ.wdgj.api.user.biz.infrastructure.common.LogFactory;
import com.differ.wdgj.api.user.biz.infrastructure.thread.pool.TaskEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;

/**
 * 库存同步请求模式抽象类
 *
 * <AUTHOR>
 * @date 2025/7/4 15:03
 */
public abstract class AbstractStockRequestMode implements IStockRequestMode {
    //region 常量
    /**
     * 标题
     */
    protected final String caption = "库存同步请求模式";
    /**
     * 默认错误信息
     */
    protected final String defaultErrorMessage = "外部未返回库存同步结果";
    /**
     * 全局上下文
     */
    protected final StockSyncContext context;
    /**
     * 上下文
     */
    private final BaseSyncStockProcessor platProcessor;
    /**
     * 批量库存同步请求
     */
    protected final BatchSyncStockApiCall stockApiCall;
    //endregion

    //region 构造

    /**
     * 构造方法
     *
     * @param context 全局上下文
     */
    public AbstractStockRequestMode(StockSyncContext context, BaseSyncStockProcessor platProcessor) {
        this.context = context;
        this.platProcessor = platProcessor;
        this.stockApiCall = new BatchSyncStockApiCall();
    }

    /**
     * 构造方法
     *
     * @param context 全局上下文
     */
    public AbstractStockRequestMode(StockSyncContext context, BaseSyncStockProcessor platProcessor, BatchSyncStockApiCall stockApiCall) {
        this.context = context;
        this.platProcessor = platProcessor;
        this.stockApiCall = stockApiCall;
    }
    //endregion

    //region 实现基类方法

    /**
     * 发起库存同步请求
     *
     * @param group 库存同步请求分组
     * @return 请求结果
     */
    @Override
    public StockContentResult<List<Future<List<StockSyncMatchExecuteResult>>>> requestSyncStock(StockSyncRequestGroup group) {
        try {
            // 执行具体的库存获取逻辑
            return doRequest(group);
        } catch (Exception e) {
            String message = String.format("【%s】【%s】库存同步请求失败，原因：%s",
                    context.getVipUser(), caption(), e.getMessage());
            LogFactory.error(caption, message, e);
            return StockContentResult.failed(message);
        }
    }
    //endregion

    //region 抽象方法

    /**
     * 发起库存同步请求
     *
     * @param group 库存同步请求分组
     * @return 结果
     */
    public abstract StockContentResult<List<Future<List<StockSyncMatchExecuteResult>>>> doRequest(StockSyncRequestGroup group);

    /**
     * 获取标题
     *
     * @return 标题
     */
    public abstract String caption();
    //endregion

    //region 供子类调用

    /**
     * 获取库存同步请求线程池
     *
     * @return 线程池
     */
    public final TaskEnum getStockRequestTaskTool() {
        // 手动库存同步独立线程池
        if (StockSyncTriggerTypeEnum.MANUAL_SYNC.equals(this.context.getTriggerType())) {
            return TaskEnum.API_STOCK_POLY_REQUEST_MANUAL;
        }

        // 自动库存同步慢速模式
        if(context.getSlowSpeedSign()){
            return TaskEnum.API_STOCK_POLY_REQUEST_SLOW_PLAT;
        }

        // 自动库存同步独立线程池
        return TaskEnum.API_STOCK_POLY_REQUEST_AUTO;
    }

    /**
     * 同步执行请求
     *
     * @param requestItem 请求
     * @return 请求结果
     */
    public final List<StockSyncMatchExecuteResult> executeRequest(StockSyncRequestItem requestItem) {
        List<StockSyncMatchExecuteResult> matchResults = new ArrayList<>();
        try {
            // 请求库存同步
            StopWatch stopWatch = StopWatchUtils.starDiffTime();
            ApiCallResponse<PolyAPIBusinessBatchSyncStockResponse> response = this.requestStockSync(requestItem.getRequest());
            long requestTimeSpent = StopWatchUtils.diffTime(stopWatch);

            // 记录响应结果
            requestItem.setResponse(response);
            // 处理响应结果
            boolean isEmptyResponse = response == null || response.getBizData() == null || CollectionUtils.isEmpty(response.getBizData().getResults());
            requestItem.getGoodsRequestMapping().forEach((guidEnhance, requestPair) -> {
                // 基础数据
                GoodsMatchEnhance matchEnhance = requestPair.getMatchEnhance();
                GoodsStockCalculationResult goodsStockInfo = requestPair.getGoodsStockInfo();
                BusinessBatchSyncStockRequestGoodInfo goodsPolyRequest = requestPair.getGoodsInfo();

                // 构建默认商品规格级菠萝派结果
                if (isEmptyResponse) {
                    String message = response == null
                            ? defaultErrorMessage
                            : StringUtils.defaultString(response.getSubMessage(), defaultErrorMessage);
                    matchResults.add(StockSyncMatchExecuteResult.filedResult(matchEnhance, message));
                    return;
                }

                // 匹配平台响应
                List<BusinessBatchSyncStockResponseGoodSyncStockResultItem> goodsPolyResponses = response.getBizData().getResults();
                BusinessBatchSyncStockResponseGoodSyncStockResultItem goodsPolyResponse = goodsPolyResponses.stream().filter(x -> defaultMatchGoodsResult(goodsPolyRequest, x) && platProcessor.platMatchGoodsResponse(goodsPolyRequest, x)).findFirst().orElse(null);
                if (goodsPolyResponse == null) {
                    String message = String.format("菠萝派返回未匹配到对应[平台商品Id:%s,平台规格Id:%s,多仓标识:%s]对应库存同步数据", goodsPolyRequest.getPlatProductId(), goodsPolyRequest.getSkuId(), matchEnhance.getMultiSign());
                    matchResults.add(StockSyncMatchExecuteResult.filedResult(matchEnhance, message));
                    return;
                }

                matchResults.add(StockSyncMatchExecuteResult.successResult(matchEnhance, goodsStockInfo.getStockDetailMap(), goodsPolyRequest, goodsPolyResponse));
            });

            // todo 记录日志

            // todo 耗时监控
        } catch (Exception e) {
            // 记录商品级结果
            String matchMessage = "请求外部接口库存同步系统异常";
            requestItem.getGoodsRequestMapping().forEach((guidEnhance, requestPair) -> {
                matchResults.add(StockSyncMatchExecuteResult.filedResult(requestPair.getMatchEnhance(), matchMessage));
            });
            // 记录异常日志
            String errorMessage = String.format("[%s-%s]商品匹配库存同步同步请求失败，请求列表：%s", context.getVipUser(), context.getShopId(), JsonUtils.toJson(requestItem.getRequest()));
            LogFactory.error("库存同步同步请求", errorMessage, e);
        }

        return matchResults;
    }

    /**
     * 请求库存同步
     *
     * @param request 请求
     * @return 响应
     */
    protected final ApiCallResponse<PolyAPIBusinessBatchSyncStockResponse> requestStockSync(PolyAPIBusinessBatchSyncStockRequest request) {
        // 请求菠萝派
        return this.stockApiCall.apiCall(context.getVipUser(), context.getShopId(), request);
    }

    //endregion

    //region 私有方法

    /**
     * 默认匹配商品级结果
     *
     * @param goodsPolyRequest  商品级请求信息
     * @param goodsPolyResponse 商品级返回信息
     * @return 匹配结果
     */
    private boolean defaultMatchGoodsResult(BusinessBatchSyncStockRequestGoodInfo goodsPolyRequest, BusinessBatchSyncStockResponseGoodSyncStockResultItem goodsPolyResponse) {
        // 平台商品ID
        boolean platProductIdResult = StringUtils.equals(goodsPolyRequest.getPlatProductId(), goodsPolyResponse.getPlatProductId());
        // 平台规格Id
        boolean platSkuIdResult = StringUtils.equals(goodsPolyRequest.getSkuId(), goodsPolyResponse.getSkuId());
        // 平台店铺Id
        boolean platStoreIdResult = StringUtils.isEmpty(goodsPolyRequest.getPlatStoreId()) || StringUtils.equals(goodsPolyRequest.getPlatStoreId(), goodsPolyResponse.getPlatStoreId());
        // 平台仓库编码
        boolean whseCodeResult = StringUtils.isEmpty(goodsPolyRequest.getWhseCode()) || StringUtils.equals(goodsPolyRequest.getWhseCode(), goodsPolyResponse.getWhseCode());
        // 平台批次号（常态合作编码）
        boolean vipCooperationNoResult = StringUtils.isEmpty(goodsPolyRequest.getVipCooperationNo()) || StringUtils.equals(goodsPolyRequest.getVipCooperationNo(), goodsPolyResponse.getVipCooperationNo());
        // 匹配结果
        return platProductIdResult && platSkuIdResult && platStoreIdResult && whseCodeResult && vipCooperationNoResult;
    }
    //endregion
}
