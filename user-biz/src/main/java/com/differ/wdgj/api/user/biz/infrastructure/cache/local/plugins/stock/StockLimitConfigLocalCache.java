package com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.stock;

import com.differ.wdgj.api.component.util.json.JsonUtils;

import com.differ.wdgj.api.user.biz.domain.stock.data.auto.StockLimitExtraConfigContent;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncSlowSpeedEnum;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.stock.StockLimitConfigCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.DevStockLimitConfigDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.StockLimitConfigLocalDto;
import com.differ.wdgj.api.user.biz.infrastructure.utils.ConfigKeyUtils;
import com.mchange.lang.IntegerUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;


/**
 * 库存限制配置内存缓存
 *
 * <AUTHOR>
 * @date 2025/8/4 14:03
 */
public class StockLimitConfigLocalCache extends AbstractLocalCache<String, List<StockLimitConfigLocalDto>> {
    //region 常量
    /**
     * 默认缓存键
     */
    private static final String DEFAULT_CACHE_KEY = "ALL";
    //endregion

    //region 构造

    /**
     * 私有构造函数
     */
    private StockLimitConfigLocalCache() {
        // 设置缓存参数
        this.cacheMaxSize = 100;
        this.expire = 30;
        this.timeUnit = TimeUnit.MINUTES;
        this.initialCapacity = 16;
    }
    //endregion

    //region 单例

    /**
     * 枚举单例
     *
     * @return 库存限制配置内存缓存单例
     */
    public static StockLimitConfigLocalCache singleton() {
        return SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final StockLimitConfigLocalCache instance;

        private SingletonEnum() {
            instance = new StockLimitConfigLocalCache();
        }
    }
    //endregion

    //region 增强方法

    /**
     * 获取所有配置
     *
     * @return 所有配置列表
     */
    public List<StockLimitConfigLocalDto> getAllConfigs() {
        return this.getCacheThenSource(DEFAULT_CACHE_KEY);
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        invalidateAll();
    }
    //endregion

    //region 实现抽象方法

    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     *
     * @param key 键
     * @return 值
     */
    @Override
    protected List<StockLimitConfigLocalDto> loadSource(String key) {
        try {
            List<DevStockLimitConfigDO> allConfigs = StockLimitConfigCache.singleton().getAll();
            if (CollectionUtils.isEmpty(allConfigs)) {
                return new ArrayList<>();
            }

            List<StockLimitConfigLocalDto> localConfigs = new ArrayList<>();
            for (DevStockLimitConfigDO config : allConfigs) {
                StockLimitConfigLocalDto localConfig = new StockLimitConfigLocalDto();
                localConfig.setMemberName(Arrays.asList(config.getMemberName().split(ConfigKeyUtils.WARNING_SIGNAL)));
                localConfig.setPlat(Arrays.asList(config.getPlat().split(ConfigKeyUtils.WARNING_SIGNAL)));
                localConfig.setTriggerCount(config.getTriggerCount());
                localConfig.setSlowSpeedSign(StockSyncSlowSpeedEnum.create(IntegerUtils.parseInt(config.getExtraConfig(), -1)));
                localConfig.setSortNo(config.getSortNo());
                // 解析降频对象
                StockLimitExtraConfigContent extraConfigContent = JsonUtils.deJson(config.getExtraConfig(), StockLimitExtraConfigContent.class);
                if(extraConfigContent != null){
                    localConfig.setDowngradeFactors(extraConfigContent.getDowngradeFactors());
                }
                localConfigs.add(localConfig);
            }
            return localConfigs;
        } catch (Exception e) {
            throw new RuntimeException("加载库存限制配置数据失败", e);
        }
    }
    //endregion
}