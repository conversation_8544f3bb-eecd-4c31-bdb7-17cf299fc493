package com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger.impl;

import com.differ.wdgj.api.component.util.json.JsonUtils;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.PlatStockNoticeStatusEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncSlowSpeedEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.AutoSyncStockTriggerMessage;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.StockContentResult;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncDoTriggerInfo;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncTriggerCommand;
import com.differ.wdgj.api.user.biz.domain.stock.processor.SyncStockProcessTemplate;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.match.IStockGoodsMatchDataService;
import com.differ.wdgj.api.user.biz.domain.stock.subdomain.match.impl.StockGoodsMatchDataService;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger.IAutoStockSyncTrigger;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.constant.WdgjApiConst;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchPlatMapper;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 多仓自动库存同步触发器实现
 * 负责处理多仓库存同步触发数据，实现分布式锁、降频控制、数据完整性校验等功能
 * 
 * 与普通库存同步的区别：
 * 1. 数据源：查询plat_warehouse_code不为空的记录
 * 2. GoodsMatchEnhance构建：需要注入平台仓库代码作为multiSign
 *
 * <AUTHOR>
 * @date 2025/8/11 15:30
 */
public class MultiWarehouseAutoStockSyncTrigger implements IAutoStockSyncTrigger {
    //region 变量
    private static final Logger logger = LoggerFactory.getLogger(MultiWarehouseAutoStockSyncTrigger.class);

    /**
     * 商品匹配数据服务
     */
    private final IStockGoodsMatchDataService stockGoodsMatchDataService;

    /**
     * 库存同步处理模板
     */
    private final SyncStockProcessTemplate syncStockProcessTemplate;

    /**
     * 平台商品库存变动记录仓储
     */
    private final ApiSysMatchPlatMapper apiSysMatchPlatMapper;
    //endregion

    //region 构造
    public MultiWarehouseAutoStockSyncTrigger() {
        this.stockGoodsMatchDataService = new StockGoodsMatchDataService();
        this.syncStockProcessTemplate = new SyncStockProcessTemplate();
        this.apiSysMatchPlatMapper = BeanContextUtil.getBean(ApiSysMatchPlatMapper.class);
    }
    //endregion

    //region 实现接口方法

    /**
     * 触发库存同步
     *
     * @param request 自动库存同步触发请求
     */
    @Override
    public void triggerSync(AutoSyncStockTriggerMessage request) {
        // 基础校验
        if (request == null || CollectionUtils.isEmpty(request.getCommands())) {
            return;
        }
        if (StringUtils.isBlank(request.getMemberName())) {
            return;
        }

        try {
            // 1、基于库存同步命令查询[平台商品维度]多仓触发数据
            List<ApiSysMatchPlatDO> allPlatTriggerData = queryMultiWarehousePlatTriggerDataByCommands(request.getMemberName(), request.getCommands());
            if (CollectionUtils.isEmpty(allPlatTriggerData)) {
                return;
            }

            // 2、更新触发数据为同步中
            updatePlatRecordStatus(request.getMemberName(), allPlatTriggerData, PlatStockNoticeStatusEnum.IN_SYNC.getValue());

            // 3、获取商品匹配数据
            List<ApiSysMatchDO> goodsMatches = getGoodsMatchesByPlatRecords(request.getMemberName(), allPlatTriggerData);
            if (CollectionUtils.isEmpty(goodsMatches)) {
                return;
            }

            // 4、构建慢平台Map
            Map<PolyPlatEnum, Boolean> slowPlatMap = buildSlowPlatMap(request.getCommands());

            // 5、调用库存同步处理
            StockContentResult<?> result = invokeSyncStockProcess(request.getMemberName(), goodsMatches, allPlatTriggerData, slowPlatMap);
            if (result.isFailed()) {
                // 同步失败，恢复状态为待处理
                updatePlatRecordStatus(request.getMemberName(), allPlatTriggerData, PlatStockNoticeStatusEnum.SYNC_FAIL.getValue());
            } else {
                // 同步成功，更新状态为已完成
                updatePlatRecordStatus(request.getMemberName(), allPlatTriggerData, PlatStockNoticeStatusEnum.SYNC_SUCCESS.getValue());
            }
        } catch (Exception e) {
            logger.error("多仓库存同步触发异常 - 信息: {}", JsonUtils.toJson(request), e);
        }

    }
    //endregion

    /**
     * 构建慢平台Map
     *
     * @param commands 库存同步触发命令列表
     * @return 慢平台Map，key为平台，value为是否为慢平台
     */
    private Map<PolyPlatEnum, Boolean> buildSlowPlatMap(List<StockSyncTriggerCommand> commands) {
        Map<PolyPlatEnum, Boolean> slowPlatMap = new HashMap<>();

        if (CollectionUtils.isEmpty(commands)) {
            return slowPlatMap;
        }

        for (StockSyncTriggerCommand command : commands) {
            if (CollectionUtils.isNotEmpty(command.getPlats())) {
                // 判断是否为慢平台：slowSpeed为SLOW_SPEED_1时为慢平台
                boolean isSlowPlat = command.getSlowSpeed() != null &&
                        StockSyncSlowSpeedEnum.SLOW_SPEED_1.equals(command.getSlowSpeed());

                // 为该命令中的所有平台设置慢平台标识
                for (PolyPlatEnum plat : command.getPlats()) {
                    slowPlatMap.put(plat, isSlowPlat);
                }
            }
        }

        return slowPlatMap;
    }

    /**
     * 基于库存同步命令查询[平台商品维度]多仓触发数据
     * 循环命令查询所有[平台商品维度]多仓触发数据，合并后返回
     *
     * @param memberName 会员名
     * @param commands   库存同步命令列表
     * @return 平台触发数据列表
     */
    private List<ApiSysMatchPlatDO> queryMultiWarehousePlatTriggerDataByCommands(String memberName, List<StockSyncTriggerCommand> commands) {
        List<ApiSysMatchPlatDO> allPlatTriggerData = new ArrayList<>();

        return DBSwitchUtil.doDBWithUser(memberName, () -> {
            // 循环命令查询所有[平台商品维度]多仓触发数据
            for (StockSyncTriggerCommand command : commands) {
                if (CollectionUtils.isEmpty(command.getPlats())) {
                    continue;
                }

                try {
                    // 提取平台类型列表
                    List<Integer> platforms = command.getPlats().stream()
                            .map(PolyPlatEnum::getValue)
                            .collect(Collectors.toList());

                    // 使用SQL中的plat in()逻辑进行查询多仓数据
                    List<ApiSysMatchPlatDO> platTriggerData = apiSysMatchPlatMapper.selectMultiWarehouseByPlatformsAndStatus(
                            platforms,
                            PlatStockNoticeStatusEnum.WAIT_SYNC.getValue(), // 状态：1表示待处理
                            command.getTriggerCount() != null ? command.getTriggerCount() : 100
                    );

                    if (CollectionUtils.isNotEmpty(platTriggerData)) {
                        allPlatTriggerData.addAll(platTriggerData);
                    }
                } catch (Exception e) {
                    logger.error("查询多仓平台触发数据失败 - 会员: {}, 平台列表: {}", memberName, command.getPlats(), e);
                }
            }

            return allPlatTriggerData;
        });
    }

    /**
     * 更新平台触发记录状态
     *
     * @param memberName  会员名
     * @param platRecords 平台触发记录列表
     * @param status      状态值
     */
    private void updatePlatRecordStatus(String memberName, List<ApiSysMatchPlatDO> platRecords, int status) {
        if (CollectionUtils.isEmpty(platRecords)) {
            return;
        }

        DBSwitchUtil.doDBWithUser(memberName, () -> {
            try {
                // 提取记录ID列表
                List<Long> ids = platRecords.stream()
                        .map(ApiSysMatchPlatDO::getId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(ids)) {
                    // 判断是否需要清除change_source字段（状态为成功2或失败时需要清除）
                    boolean clearChangeSource = (status == PlatStockNoticeStatusEnum.SYNC_SUCCESS.getValue() || status == PlatStockNoticeStatusEnum.SYNC_FAIL.getValue());
                    // 批量更新状态
                    apiSysMatchPlatMapper.batchUpdateStatus(ids, status, clearChangeSource);
                }
            } catch (Exception e) {
                logger.error("更新多仓平台触发记录状态失败 - 会员: {}", memberName, e);
            }
            return null;
        });
    }

    /**
     * 根据平台触发记录获取商品匹配数据
     *
     * @param vipUser     会员名
     * @param platRecords 平台触发记录列表
     * @return 商品匹配数据列表
     */
    private List<ApiSysMatchDO> getGoodsMatchesByPlatRecords(String vipUser, List<ApiSysMatchPlatDO> platRecords) {
        if (CollectionUtils.isEmpty(platRecords)) {
            return new ArrayList<>();
        }

        // 提取所有的apiSysMatchId
        List<Integer> apiSysMatchIds = platRecords.stream()
                .map(ApiSysMatchPlatDO::getApiSysMatchId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(apiSysMatchIds)) {
            return new ArrayList<>();
        }

        // 查询商品匹配数据
        return stockGoodsMatchDataService.queryMatch(vipUser, apiSysMatchIds);
    }

    /**
     * 调用库存同步处理
     *
     * @param memberName        会员名
     * @param goodsMatches      商品匹配数据列表
     * @param platTriggerData   平台触发数据列表（用于获取平台仓库代码）
     * @param slowPlatMap       慢平台
     */
    private StockContentResult<?> invokeSyncStockProcess(String memberName, List<ApiSysMatchDO> goodsMatches, 
                                                        List<ApiSysMatchPlatDO> platTriggerData, Map<PolyPlatEnum, Boolean> slowPlatMap) {
        try {
            // 构建库存同步触发信息
            StockSyncDoTriggerInfo triggerInfo = new StockSyncDoTriggerInfo();
            triggerInfo.setTriggerType(StockSyncTriggerTypeEnum.AUTO_SYNC);
            triggerInfo.setOperatorName(WdgjApiConst.AUTO_OPERATOR);
            triggerInfo.setSlowPlatMap(slowPlatMap);

            // 构建平台仓库代码映射Map，key为apiSysMatchId，value为平台仓库代码
            Map<Integer, String> platWarehouseCodeMap = platTriggerData.stream()
                    .filter(x -> x.getApiSysMatchId() != null && StringUtils.isNotBlank(x.getPlatWarehouseCode()))
                    .collect(Collectors.toMap(
                            ApiSysMatchPlatDO::getApiSysMatchId,
                            ApiSysMatchPlatDO::getPlatWarehouseCode,
                            (existing, replacement) -> existing // 如果有重复key，保留第一个
                    ));

            // 转换为GoodsMatchEnhance列表，注入平台仓库代码
            List<GoodsMatchEnhance> goodsMatchEnhances = goodsMatches.stream()
                    .map(x -> {
                        String platWarehouseCode = platWarehouseCodeMap.getOrDefault(x.getId(), StringUtils.EMPTY);
                        return GoodsMatchEnhance.create(x, platWarehouseCode);
                    })
                    .collect(Collectors.toList());
            
            triggerInfo.setGoodsMatchEnhance(goodsMatchEnhances);

            // 调用库存同步处理模板
            return syncStockProcessTemplate.doSyncStock(memberName, triggerInfo);
        } catch (Exception e) {
            String message = String.format("调用多仓库存同步处理异常 - 会员: %s, 异常信息: %s", memberName, e.getMessage());
            logger.error(message, e);
            return StockContentResult.failed(message);
        }
    }
}
