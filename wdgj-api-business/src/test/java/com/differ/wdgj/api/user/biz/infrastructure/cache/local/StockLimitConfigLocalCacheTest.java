package com.differ.wdgj.api.user.biz.infrastructure.cache.local;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.stock.StockLimitConfigLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.stock.StockLimitConfigCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.DevStockLimitConfigDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.StockLimitConfigLocalDto;
import org.junit.Assert;
import org.junit.Test;

import java.util.List;

/**
 * 库存限制配置内存缓存集成测试
 *
 * <AUTHOR> Generated
 * @date 2025/1/27
 */
public class StockLimitConfigLocalCacheTest extends AbstractSpringTest {

    //region 基础功能测试
    
    /**
     * 测试单例模式
     */
    @Test
    public void singletonTest() {
        // Arrange & Act
        StockLimitConfigLocalCache instance1 = StockLimitConfigLocalCache.singleton();
        StockLimitConfigLocalCache instance2 = StockLimitConfigLocalCache.singleton();
        
        // Assert
        Assert.assertSame("单例模式验证失败", instance1, instance2);
    }
    
    /**
     * 测试获取所有配置
     */
    @Test
    public void getAllConfigsTest() {
        // Arrange
        StockLimitConfigLocalCache cache = StockLimitConfigLocalCache.singleton();
        
        // Act
        List<StockLimitConfigLocalDto> configs = cache.getAllConfigs();
        
        // Assert
        Assert.assertNotNull("配置列表不应为null", configs);
    }
    
    //endregion
    
    //region 缓存管理测试
    
    /**
     * 测试清除缓存
     */
    @Test
    public void clearCacheTest() {
        // Arrange
        StockLimitConfigLocalCache cache = StockLimitConfigLocalCache.singleton();
        
        // Act & Assert
        try {
            cache.clearCache();
            Assert.assertTrue("清除缓存应该成功", true);
        } catch (Exception e) {
            Assert.fail("清除缓存不应该抛出异常: " + e.getMessage());
        }
    }
    
    /**
     * 测试缓存机制
     */
    @Test
    public void cacheTest() {
        // Arrange
        StockLimitConfigLocalCache cache = StockLimitConfigLocalCache.singleton();
        
        // Act
        List<StockLimitConfigLocalDto> configs1 = cache.getAllConfigs();
        List<StockLimitConfigLocalDto> configs2 = cache.getAllConfigs();
        
        // Assert
        Assert.assertNotNull("第一次获取配置不应为null", configs1);
        Assert.assertNotNull("第二次获取配置不应为null", configs2);
    }
    
    //endregion
    
    //region 数据验证测试
    
    /**
     * 测试数据结构验证
     */
    @Test
    public void dataStructureValidationTest() {
        // Arrange
        StockLimitConfigLocalCache cache = StockLimitConfigLocalCache.singleton();
        
        // Act
        List<StockLimitConfigLocalDto> configs = cache.getAllConfigs();
        
        // Assert
        Assert.assertNotNull("配置列表不应为null", configs);
        for (StockLimitConfigLocalDto config : configs) {
            if (config.getMemberName() != null) {
                Assert.assertNotNull("会员名不应为null", config.getMemberName());
            }
            if (config.getPlat() != null) {
                Assert.assertNotNull("平台不应为null", config.getPlat());
            }
            if (config.getSortNo() != null) {
                Assert.assertTrue("排序序号应该大于等于0", config.getSortNo() >= 0);
            }
        }
    }
    
    /**
     * 测试空配置处理
     */
    @Test
    public void emptyConfigHandlingTest() {
        // Arrange
        StockLimitConfigLocalCache cache = StockLimitConfigLocalCache.singleton();
        
        // Act
        List<StockLimitConfigLocalDto> configs = cache.getAllConfigs();
        
        // Assert
        Assert.assertNotNull("配置列表不应为null", configs);
        // 空配置也是有效的测试场景
    }
    
    //endregion
    
    //region 集成测试
    
    /**
     * 测试与Redis缓存的集成
     */
    @Test
    public void redisIntegrationTest() {
        // Arrange
        StockLimitConfigCache redisCache = StockLimitConfigCache.singleton();
        StockLimitConfigLocalCache localCache = StockLimitConfigLocalCache.singleton();
        
        // Act
        List<DevStockLimitConfigDO> redisConfigs = redisCache.getAll();
        List<StockLimitConfigLocalDto> localConfigs = localCache.getAllConfigs();
        
        // Assert
        Assert.assertNotNull("Redis配置列表不应为null", redisConfigs);
        Assert.assertNotNull("本地配置列表不应为null", localConfigs);
    }
    
    //endregion
}