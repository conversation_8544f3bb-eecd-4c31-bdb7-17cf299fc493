package com.differ.wdgj.api.user.biz.domain.stock.data.trigger;

import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.GoodsMatchEnhance;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerTypeEnum;

import java.util.List;
import java.util.Map;

/**
 * 库存同步请求
 *
 * <AUTHOR>
 * @date 2024-02-26 15:05
 */
public class StockSyncDoTriggerInfo {
    /**
     * 库存同步触发数据列表
     */
    private List<GoodsMatchEnhance> goodsMatchEnhance;

    /**
     * 触发类型
     */
    private StockSyncTriggerTypeEnum triggerType;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 慢平台
     */
    private Map<PolyPlatEnum, Boolean> slowPlatMap;

    //region get/set

    public StockSyncTriggerTypeEnum getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(StockSyncTriggerTypeEnum triggerType) {
        this.triggerType = triggerType;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public List<GoodsMatchEnhance> getGoodsMatchEnhance() {
        return goodsMatchEnhance;
    }

    public void setGoodsMatchEnhance(List<GoodsMatchEnhance> goodsMatchEnhance) {
        this.goodsMatchEnhance = goodsMatchEnhance;
    }

    public Map<PolyPlatEnum, Boolean> getSlowPlatMap() {
        return slowPlatMap;
    }

    public void setSlowPlatMap(Map<PolyPlatEnum, Boolean> slowPlatMap) {
        this.slowPlatMap = slowPlatMap;
    }

    //endregion
}
