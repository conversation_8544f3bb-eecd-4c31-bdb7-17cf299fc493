package com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.command.plugins;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerConfigTypeEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncTriggerCommand;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.command.AbstractAutoStockSyncCommandResolver;

import java.util.List;

/**
 * 自动库存同步触发命令处理 - 多仓库存同步
 * 
 * 负责处理多仓库存同步的命令解析逻辑
 * 与普通库存同步的区别在于配置类型为MULTI_WARE
 *
 * <AUTHOR>
 * @date 2025/8/11 15:45
 */
public class MultiWarehouseAutoStockSyncCommandResolver extends AbstractAutoStockSyncCommandResolver {
    /**
     * 获取配置类型
     *
     * @return 配置类型
     */
    @Override
    protected StockSyncTriggerConfigTypeEnum getConfigType() {
        return StockSyncTriggerConfigTypeEnum.MULTI_WARE;
    }

    /**
     * 过滤命令
     * 多仓库存同步不需要特殊的过滤逻辑，返回所有命令
     *
     * @param commands 命令列表
     * @return 过滤后的命令列表
     */
    @Override
    protected List<StockSyncTriggerCommand> filterCommand(List<StockSyncTriggerCommand> commands) {
        // 多仓库存同步不需要特殊的过滤逻辑，返回所有命令
        return commands;
    }
}
