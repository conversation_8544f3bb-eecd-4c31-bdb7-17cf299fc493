package com.differ.wdgj.api.user.biz.infrastructure.cache.local.plugins.stock;

import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncTriggerConfigTypeEnum;
import com.differ.wdgj.api.user.biz.infrastructure.cache.local.core.AbstractLocalCache;
import com.differ.wdgj.api.user.biz.infrastructure.cache.remote.stock.StockTriggerConfigCache;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.center.DevStockTriggerConfigDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.dto.cache.local.StockTriggerConfigLocalDto;
import org.apache.commons.lang3.RandomUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 库存触发配置内存缓存
 *
 * <AUTHOR>
 * @date 2025/8/6 09:30
 */
public class StockTriggerConfigLocalCache extends AbstractLocalCache<StockSyncTriggerConfigTypeEnum, List<StockTriggerConfigLocalDto>> {
    //region 构造
    private StockTriggerConfigLocalCache(){
        // 缓存有效时间-分钟
        this.expire = RandomUtils.nextInt(60, 180);
        this.timeUnit = TimeUnit.SECONDS;
        this.cacheMaxSize = 100;
    }
    //endregion

    //region 单例
    /**
     * 枚举单例
     *
     * @return 配置键Redis缓存单例
     */
    public static StockTriggerConfigLocalCache singleton() {
        return StockTriggerConfigLocalCache.SingletonEnum.SINGLETON.instance;
    }

    private enum SingletonEnum {
        /**
         * 单例
         */
        SINGLETON;

        private final StockTriggerConfigLocalCache instance;

        private SingletonEnum() {
            instance = new StockTriggerConfigLocalCache();
        }
    }

    //endregion

    //region 实现抽象方法
    /**
     * 当缓存不存在时，会调用此函数来加载数据源
     *
     * @param key 键
     * @return 值
     */
    @Override
    protected List<StockTriggerConfigLocalDto> loadSource(StockSyncTriggerConfigTypeEnum key) {
        // 通过Rides缓存获取所有配置
        List<DevStockTriggerConfigDO> stockTriggerConfigAll = StockTriggerConfigCache.singleton().getAll();
        // 获取当前类型配置
        List<DevStockTriggerConfigDO> stockTriggerConfigs = stockTriggerConfigAll.stream().filter(x -> key.getValue().equals(x.getConfigType())).collect(Collectors.toList());

        List<StockTriggerConfigLocalDto> dtoList = new ArrayList<>();
        stockTriggerConfigs.forEach(config -> {
            StockTriggerConfigLocalDto dto = new StockTriggerConfigLocalDto();
            dto.setConfigType(StockSyncTriggerConfigTypeEnum.create(config.getConfigType()));
            dto.setMemberName(config.getMemberName());
            dto.setTriggerCount(config.getTriggerCount());
            dto.setControlFactor(config.getControlFactor());
            dto.setSortNo(config.getSortNo());
            dtoList.add(dto);
        });

        return dtoList;
    }
    //endregion
}
