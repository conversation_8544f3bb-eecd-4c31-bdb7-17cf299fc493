package com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger;

import com.differ.wdgj.api.AbstractSpringTest;
import com.differ.wdgj.api.component.util.spring.BeanContextUtil;
import com.differ.wdgj.api.user.biz.domain.apicall.data.enums.PolyPlatEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.PlatStockNoticeStatusEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.enums.StockSyncSlowSpeedEnum;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.AutoSyncStockTriggerMessage;
import com.differ.wdgj.api.user.biz.domain.stock.data.trigger.StockSyncTriggerCommand;
import com.differ.wdgj.api.user.biz.domain.stock.trigger.auto.trigger.impl.MultiWarehouseAutoStockSyncTrigger;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchDO;
import com.differ.wdgj.api.user.biz.infrastructure.data.api.user.ApiSysMatchPlatDO;
import com.differ.wdgj.api.user.biz.infrastructure.repository.core.DBSwitchUtil;
import com.differ.wdgj.api.user.biz.infrastructure.repository.mapper.user.ApiSysMatchPlatMapper;
import org.apache.commons.collections.CollectionUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * MultiWarehouseAutoStockSyncTrigger集成测试
 * 直接操作数据库，验证多仓库存同步业务逻辑的数据库操作结果
 *
 * <AUTHOR> Generated
 * @date 2025-01-27
 */
//@Ignore
public class MultiWarehouseAutoStockSyncTriggerTest extends AbstractSpringTest {

    //region 变量
    private MultiWarehouseAutoStockSyncTrigger multiWarehouseAutoStockSyncTrigger;

    @Autowired
    private ApiSysMatchPlatMapper apiSysMatchPlatMapper;

    private static final String TEST_MEMBER_NAME = "api2017";
    private List<Long> testDataIds = new ArrayList<>();
    //endregion

    //region 前置执行
    @Before
    public void setUp() {
        // 清理可能存在的测试数据
        cleanupTestData();

        multiWarehouseAutoStockSyncTrigger = new MultiWarehouseAutoStockSyncTrigger();
        apiSysMatchPlatMapper = BeanContextUtil.getBean(ApiSysMatchPlatMapper.class);
    }
    //endregion

    //region 后置执行
    @After
    public void tearDown() {
        // 清理测试数据
        cleanupTestData();
    }
    //endregion

    /**
     * 测试正常流程
     * 验证触发同步后数据库状态的变化
     */
    @Test
    public void triggerSyncNormalFlowTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestMultiWarehousePlatData();
        insertTestPlatData(testData);

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 执行测试
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
        Assert.assertTrue("应该有数据被更新", updatedData.size() > 0);

        // 验证状态已更新（具体状态值需要根据实际业务逻辑确定）
        for (ApiSysMatchPlatDO platDO : updatedData) {
            Assert.assertNotEquals("状态应该已被更新", Integer.valueOf(1), platDO.getStatus());
        }
    }

    /**
     * 测试空请求处理
     */
    @Test
    public void triggerSyncWithNullRequestTest() {
        try {
            multiWarehouseAutoStockSyncTrigger.triggerSync(null);
            // 如果没有抛出异常，验证数据库没有被意外修改
            List<ApiSysMatchPlatDO> data = queryTestPlatData();
            Assert.assertTrue("空请求不应该影响数据库", data.isEmpty());
        } catch (Exception e) {
            // 预期可能抛出异常，这是正常的
            Assert.assertNotNull("异常信息不应为空", e.getMessage());
        }
    }

    /**
     * 测试空命令列表处理
     */
    @Test
    public void triggerSyncWithEmptyCommandsTest() {
        // 构造空命令请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(new ArrayList<>());

        // 执行测试
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态
        List<ApiSysMatchPlatDO> data = queryTestPlatData();
        Assert.assertTrue("空命令不应该产生数据", data.isEmpty());
    }

    /**
     * 测试空会员名处理
     */
    @Test
    public void triggerSyncWithBlankMemberNameTest() {
        // 构造空会员名请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName("");
        request.setCommands(createTestCommands());

        try {
            multiWarehouseAutoStockSyncTrigger.triggerSync(request);
            // 验证数据库状态
            List<ApiSysMatchPlatDO> data = queryTestPlatData();
            Assert.assertTrue("空会员名不应该产生数据", data.isEmpty());
        } catch (Exception e) {
            // 预期可能抛出异常
            Assert.assertNotNull("异常信息不应为空", e.getMessage());
        }
    }

    /**
     * 测试多仓库存同步特性
     * 验证带有仓库代码的记录能被正确处理
     */
    @Test
    public void triggerSyncMultiWarehouseTest() {
        // 准备测试数据（带有仓库代码）
        List<ApiSysMatchPlatDO> testData = createTestMultiWarehousePlatData();
        insertTestPlatData(testData);

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 执行测试
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态变化
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
        Assert.assertTrue("应该有数据被更新", updatedData.size() > 0);

        // 验证带有仓库代码的记录状态已更新
        for (ApiSysMatchPlatDO platDO : updatedData) {
            if (platDO.getPlatWarehouseCode() != null && !platDO.getPlatWarehouseCode().isEmpty()) {
                Assert.assertNotEquals("带仓库代码的记录状态应该已被更新", Integer.valueOf(1), platDO.getStatus());
            }
        }
    }

    /**
     * 测试一对多仓库代码映射逻辑
     * 验证一个apiSysMatchId对应多个平台仓库代码的场景
     */
    @Test
    public void testOneToManyWarehouseMapping() {
        // 模拟测试数据
        List<ApiSysMatchPlatDO> platTriggerData = createTestPlatTriggerData();
        List<ApiSysMatchDO> goodsMatches = createTestGoodsMatches();

        // 验证测试数据：一个apiSysMatchId(1001)对应三个仓库代码(WH001, WH002, WH003)
        long matchId1001Count = platTriggerData.stream()
                .filter(x -> x.getApiSysMatchId().equals(1001L))
                .count();

        Assert.assertEquals("应该有3个仓库代码对应同一个apiSysMatchId", 3, matchId1001Count);

        // 验证仓库代码不重复
        Set<String> warehouseCodes = platTriggerData.stream()
                .filter(x -> x.getApiSysMatchId().equals(1001L))
                .map(ApiSysMatchPlatDO::getPlatWarehouseCode)
                .collect(Collectors.toSet());

        Assert.assertEquals("仓库代码应该不重复", 3, warehouseCodes.size());
        Assert.assertTrue("应该包含WH001", warehouseCodes.contains("WH001"));
        Assert.assertTrue("应该包含WH002", warehouseCodes.contains("WH002"));
        Assert.assertTrue("应该包含WH003", warehouseCodes.contains("WH003"));
    }

    /**
     * 测试异常会员名处理
     */
    @Test
    public void triggerSyncWithInvalidMemberNameTest() {
        // 构造异常会员名请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName("invalid_member");
        request.setCommands(createTestCommands());

        try {
            multiWarehouseAutoStockSyncTrigger.triggerSync(request);
            // 验证数据库状态
            List<ApiSysMatchPlatDO> data = queryTestPlatData();
            Assert.assertTrue("无效会员名不应该产生数据", data.isEmpty());
        } catch (Exception e) {
            // 预期可能抛出异常
            Assert.assertNotNull("异常信息不应为空", e.getMessage());
        }
    }

    /**
     * 测试并发处理
     * 注意：此测试可能需要在实际环境中进行更复杂的设置
     */
    @Test
    public void concurrentTriggerSyncTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestMultiWarehousePlatData();
        insertTestPlatData(testData);

        // 构造请求
        final AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 模拟并发调用
        Runnable task = () -> multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 执行两次调用
        Thread thread1 = new Thread(task);
        Thread thread2 = new Thread(task);

        thread1.start();
        thread2.start();

        try {
            thread1.join();
            thread2.join();
        } catch (InterruptedException e) {
            Assert.fail("并发测试中断: " + e.getMessage());
        }

        // 验证数据库状态
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
        Assert.assertTrue("应该有数据被更新", updatedData.size() > 0);
    }

    /**
     * 测试性能
     * 验证处理大量数据的性能
     */
    @Test
    public void performanceTest() {
        // 准备大量测试数据
        List<ApiSysMatchPlatDO> testData = createLargeTestData(50);
        insertTestPlatData(testData);

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行测试
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证性能（这里的阈值需要根据实际情况调整）
        Assert.assertTrue("处理时间应在可接受范围内", duration < 5000);

        // 验证数据库状态
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        Assert.assertNotNull("更新后的数据不应为空", updatedData);
        Assert.assertTrue("应该有数据被更新", updatedData.size() > 0);
    }

    /**
     * 测试数据一致性
     * 验证触发后数据状态的一致性
     */
    @Test
    public void dataConsistencyTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestMultiWarehousePlatData();
        insertTestPlatData(testData);

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 执行测试
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);

        // 验证数据库状态一致性
        List<ApiSysMatchPlatDO> updatedData = queryTestPlatData();
        
        // 验证所有数据状态一致
        boolean allConsistent = true;
        Integer expectedStatus = null;
        
        for (ApiSysMatchPlatDO platDO : updatedData) {
            if (expectedStatus == null) {
                expectedStatus = platDO.getStatus();
            } else if (!expectedStatus.equals(platDO.getStatus())) {
                allConsistent = false;
                break;
            }
        }
        
        Assert.assertTrue("所有数据状态应该一致", allConsistent);
    }

    /**
     * 测试幂等性
     * 验证多次触发同一请求的结果一致性
     */
    @Test
    public void idempotencyTest() {
        // 准备测试数据
        List<ApiSysMatchPlatDO> testData = createTestMultiWarehousePlatData();
        insertTestPlatData(testData);

        // 构造请求
        AutoSyncStockTriggerMessage request = new AutoSyncStockTriggerMessage();
        request.setMemberName(TEST_MEMBER_NAME);
        request.setCommands(createTestCommands());

        // 第一次执行
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);
        List<ApiSysMatchPlatDO> firstResult = queryTestPlatData();

        // 第二次执行相同请求
        multiWarehouseAutoStockSyncTrigger.triggerSync(request);
        List<ApiSysMatchPlatDO> secondResult = queryTestPlatData();

        // 验证两次结果一致
        Assert.assertEquals("两次执行后数据数量应该一致", firstResult.size(), secondResult.size());
        
        // 验证每条数据的状态一致
        for (int i = 0; i < firstResult.size(); i++) {
            Assert.assertEquals("两次执行后数据状态应该一致", 
                    firstResult.get(i).getStatus(), 
                    secondResult.get(i).getStatus());
        }
    }

    //region 辅助方法
    /**
     * 创建测试触发消息
     */
    private AutoSyncStockTriggerMessage createTestTriggerMessage() {
        AutoSyncStockTriggerMessage message = new AutoSyncStockTriggerMessage();
        message.setMemberName(TEST_MEMBER_NAME);
        message.setCommands(createTestCommands());
        return message;
    }

    /**
     * 创建测试命令列表
     */
    private List<StockSyncTriggerCommand> createTestCommands() {
        List<StockSyncTriggerCommand> commands = new ArrayList<>();
        
        // 创建测试命令
        StockSyncTriggerCommand command1 = new StockSyncTriggerCommand();
        command1.setApiSysMatchId(1001L);
        command1.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL.getCode());
        commands.add(command1);
        
        StockSyncTriggerCommand command2 = new StockSyncTriggerCommand();
        command2.setApiSysMatchId(1002L);
        command2.setSlowSpeed(StockSyncSlowSpeedEnum.NORMAL.getCode());
        commands.add(command2);
        
        return commands;
    }

    /**
     * 创建测试平台触发数据
     */
    private List<ApiSysMatchPlatDO> createTestPlatTriggerData() {
        List<ApiSysMatchPlatDO> platTriggerData = new ArrayList<>();
        
        // 创建一对多映射测试数据（一个apiSysMatchId对应多个仓库代码）
        ApiSysMatchPlatDO platDO1 = new ApiSysMatchPlatDO();
        platDO1.setId(10001L);
        platDO1.setApiSysMatchId(1001L);
        platDO1.setPlatWarehouseCode("WH001");
        platDO1.setStatus(1);
        platDO1.setMemberName(TEST_MEMBER_NAME);
        platDO1.setPlatCode(PolyPlatEnum.TAOBAO.getCode());
        platTriggerData.add(platDO1);
        
        ApiSysMatchPlatDO platDO2 = new ApiSysMatchPlatDO();
        platDO2.setId(10002L);
        platDO2.setApiSysMatchId(1001L);
        platDO2.setPlatWarehouseCode("WH002");
        platDO2.setStatus(1);
        platDO2.setMemberName(TEST_MEMBER_NAME);
        platDO2.setPlatCode(PolyPlatEnum.TAOBAO.getCode());
        platTriggerData.add(platDO2);
        
        ApiSysMatchPlatDO platDO3 = new ApiSysMatchPlatDO();
        platDO3.setId(10003L);
        platDO3.setApiSysMatchId(1001L);
        platDO3.setPlatWarehouseCode("WH003");
        platDO3.setStatus(1);
        platDO3.setMemberName(TEST_MEMBER_NAME);
        platDO3.setPlatCode(PolyPlatEnum.TAOBAO.getCode());
        platTriggerData.add(platDO3);
        
        ApiSysMatchPlatDO platDO4 = new ApiSysMatchPlatDO();
        platDO4.setId(10004L);
        platDO4.setApiSysMatchId(1002L);
        platDO4.setPlatWarehouseCode("WH004");
        platDO4.setStatus(1);
        platDO4.setMemberName(TEST_MEMBER_NAME);
        platDO4.setPlatCode(PolyPlatEnum.TAOBAO.getCode());
        platTriggerData.add(platDO4);
        
        return platTriggerData;
    }

    /**
     * 创建测试商品匹配数据
     */
    private List<ApiSysMatchDO> createTestGoodsMatches() {
        List<ApiSysMatchDO> goodsMatches = new ArrayList<>();
        
        ApiSysMatchDO match1 = new ApiSysMatchDO();
        match1.setId(1001L);
        match1.setMemberName(TEST_MEMBER_NAME);
        match1.setPlatCode(PolyPlatEnum.TAOBAO.getCode());
        goodsMatches.add(match1);
        
        ApiSysMatchDO match2 = new ApiSysMatchDO();
        match2.setId(1002L);
        match2.setMemberName(TEST_MEMBER_NAME);
        match2.setPlatCode(PolyPlatEnum.TAOBAO.getCode());
        goodsMatches.add(match2);
        
        return goodsMatches;
    }

    /**
     * 创建多仓测试数据
     */
    /**
     * 创建多仓测试数据
     */
    private List<ApiSysMatchPlatDO> createTestMultiWarehousePlatData() {
        List<ApiSysMatchPlatDO> platData = new ArrayList<>();
        
        // 创建带有仓库代码的测试数据
        for (int i = 0; i < 5; i++) {
            ApiSysMatchPlatDO platDO = new ApiSysMatchPlatDO();
            platDO.setId(20001L + i);
            platDO.setApiSysMatchId(1001L + (i % 2)); // 交替使用两个不同的apiSysMatchId
            platDO.setPlatWarehouseCode("MWH" + (i + 1)); // 多仓代码
            platDO.setStatus(1); // 初始状态
            platDO.setMemberName(TEST_MEMBER_NAME);
            platDO.setPlatCode(PolyPlatEnum.TAOBAO.getCode());
            platDO.setCreateTime(LocalDateTime.now());
            platDO.setUpdateTime(LocalDateTime.now());
            platData.add(platDO);
            
            // 记录ID用于清理
            testDataIds.add(platDO.getId());
        }
        
        return platData;
    }
    
    /**
     * 创建大量测试数据用于性能测试
     */
    private List<ApiSysMatchPlatDO> createLargeTestData(int count) {
        List<ApiSysMatchPlatDO> platData = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            ApiSysMatchPlatDO platDO = new ApiSysMatchPlatDO();
            platDO.setId(30001L + i);
            platDO.setApiSysMatchId(1001L + (i % 5)); // 使用5个不同的apiSysMatchId
            platDO.setPlatWarehouseCode("PERF" + (i + 1)); // 性能测试仓库代码
            platDO.setStatus(1); // 初始状态
            platDO.setMemberName(TEST_MEMBER_NAME);
            platDO.setPlat(PolyPlatEnum.TAOBAO.getCode());
            platDO.setCreateTime(LocalDateTime.now());
            platDO.setUpdateTime(LocalDateTime.now());
            platData.add(platDO);
            
            // 记录ID用于清理
            testDataIds.add(platDO.getId());
        }
        
        return platData;
    }
    
    /**
     * 插入测试数据到数据库
     */
    private void insertTestPlatData(List<ApiSysMatchPlatDO> platData) {
        if (CollectionUtils.isEmpty(platData)) {
            return;
        }
        
        try {
            // 切换到用户库
            DBSwitchUtil.switchToUserDB(TEST_MEMBER_NAME);
            
            // 插入数据
            for (ApiSysMatchPlatDO platDO : platData) {
                apiSysMatchPlatMapper.insert(platDO);
                testDataIds.add(platDO.getId());
            }
        } catch (Exception e) {
             Assert.fail("插入测试数据失败: " + e.getMessage());
         }
     }
     
     /**
      * 查询测试数据
     */
    private List<ApiSysMatchPlatDO> queryTestPlatData() {
        try {
            // 切换到用户库
            DBSwitchUtil.switchToUserDB(TEST_MEMBER_NAME);
            
            // 查询测试数据
            if (CollectionUtils.isEmpty(testDataIds)) {
                return new ArrayList<>();
            }
            
            return apiSysMatchPlatMapper.selectByIds(testDataIds);
        } catch (Exception e) {
            Assert.fail("查询测试数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }
    
    /**
     * 清理测试数据
     */
    private void cleanupTestData() {
        try {
            // 切换到用户库
            DBSwitchUtil.switchToUserDB(TEST_MEMBER_NAME);
            
            // 清理测试数据
            if (CollectionUtils.isNotEmpty(testDataIds)) {
                for (Long id : testDataIds) {
                    apiSysMatchPlatMapper.deleteById(id);
                }
                testDataIds.clear();
            }
        } catch (Exception e) {
            // 清理失败不影响测试，仅记录日志
            System.err.println("清理测试数据失败: " + e.getMessage());
        }
    }
    //endregion
}
